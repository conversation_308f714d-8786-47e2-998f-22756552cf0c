package com.snszyk.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.domain.DeletePredicateRequest;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.snszyk.common.config.InfluxdbProperties;
import com.snszyk.core.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.time.Instant;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;

@Slf4j
public abstract class AbstractInfluxdbTool {

	private static final int LIMIT = 10000;

	public AbstractInfluxdbTool(InfluxDBClient influxDBClient, InfluxdbProperties influxdbProperties) {
		this.influxdbProperties = influxdbProperties;
		this.influxDBClient = influxDBClient;

	}

	private final InfluxDBClient influxDBClient;
	private final InfluxdbProperties influxdbProperties;

	protected abstract String[] configTagColumn();

	protected abstract String[] configFieldColumn();

	public synchronized void insert(String monitorId, String waveId, JSONObject object, Long time) {
		log.info("开始执行数据插入操作={}-----{}", waveId, monitorId);
		if (Func.isEmpty(waveId)) {
			log.info("表名为空，退出，不进行插入={}", waveId);
			return;
		}
		createInfluxdbSQL(waveId, object, time);
	}

	private void createInfluxdbSQL(String waveId, JSONObject object, Long time) {
		InfluxdbSQL sql = new MeasurementInfluxdb(waveId, Instant.ofEpochMilli(time));
		sql = new TagInfluxdb(sql, object, configTagColumn());
		sql = new FieldInfluxdb(sql, object, configFieldColumn());
		try {
			Point point = sql.createInsertPoint();
			this.influxDBClient.getWriteApiBlocking()
				.writePoint(point);
		} catch (Exception e) {
			log.error(e.getMessage());
//			e.printStackTrace();
		}
	}

	/**
	 * 更新数据
	 */
	public void update(String monitorId, String waveId, JSONObject object, Long time) {
		log.info("开始执行数据更新操作={}----{}", waveId, monitorId);
		createInfluxdbSQL(waveId, object, time);
	}

	public void update(String monitorId, String waveId, JSONObject object, Long time, Boolean isWaveData) {
		this.update(monitorId, waveId, object, time);
		if (isWaveData) {
			this.update(monitorId, String.format("%s_1", waveId), object, time);
		}
	}

	public List<JSONObject> queryData(Long begin, Long end, JSONObject sensorData) {
		return queryData(begin, end, sensorData, (query) -> query.addSort("_time", false));
	}

	public List<JSONObject> queryData(Long begin, Long end, JSONObject sensorData, InfluxdbSort sort) {
		String waveId = null;
		if (Func.isNotEmpty(sensorData.get("waveId"))) {
			waveId = sensorData.get("waveId").toString();
		}
		begin = configBegin(begin);
		end = configEnd(end);
		BasicInfluxdbQuery query = new BasicInfluxdbQuery(influxdbProperties.getBucket(), waveId, LIMIT, begin, end);
		for (String column : configTagColumn()) {
			if (Func.isEmpty(sensorData.get(column))) {
				continue;
			}
			query.addFilter(column, sensorData.get(column).toString(), "==");
		}
		if (sort != null) {
			sort.sort(query);
		}
		return fillData(query);
	}

	public List<JSONObject> queryData(Long begin, Long end, JSONObject sensorData, IPage<?> page) {
		return queryData(begin, end, sensorData, page, (query) -> query.addSort("_time", true));
	}

	protected Long configBegin(Long begin) {
		if (begin == null) {
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.YEAR, -1);
			begin = calendar.getTimeInMillis();
		}
		return begin;
	}

	protected Long configEnd(Long end) {
		if (end == null) {
			Calendar calendar = Calendar.getInstance();
			end = calendar.getTimeInMillis();
		}
		return end;
	}

	/**
	 * 行式查询并封装数据
	 */
	public <T> List<T> queryDataTable(Long begin, Long end, JSONObject sensorData, IPage<?> page, InfluxdbSort sort, Function<FluxRecord, T> function) {
		Assert.notNull(function, "function转换函数不能为空");
		begin = configBegin(begin);
		end = configEnd(end);
		BasicInfluxdbQuery query = new BasicInfluxdbQuery(influxdbProperties.getBucket(), configTableName(sensorData), LIMIT, begin, end);
		for (String column : configTagColumn()) {
			if (Func.isEmpty(sensorData.get(column))) {
				continue;
			}
			query.addFilter(column, sensorData.get(column).toString(), "==");
		}
		if (page != null) {
			query.setPage((int) page.getSize(), (int) page.getCurrent());
		}
		query.getQuerySQL().append(" |> pivot(rowKey: [\"_time\"], columnKey: [\"_field\"], valueColumn: \"_value\")");
		if (sort != null) {
			sort.sort(query);
		}
		String sql = query.createQuerySQL();
		log.info("当前语句：{}", sql);
		List<FluxTable> table = influxDBClient.getQueryApi().query(sql);

		List<T> data = new ArrayList<>();
		for (FluxTable fluxTable : table) {
			fluxTable.getRecords().forEach(fluxRecord -> {
				try {
					data.add(function.apply(fluxRecord));
				} catch (Exception e) {
					log.error(e.getMessage());
				}
			});
		}
		return data;

	}

	//配置查询表名
	protected String configTableName(JSONObject sensorData) {
		String b = sensorData.getString("waveId");
		if ("1".equals(sensorData.getString("hasWaveData"))) {
			b = String.format("%s_%s", b, "1");
		}
		return b;
	}

	public List<JSONObject> queryData(Long begin, Long end, JSONObject sensorData, IPage<?> page, InfluxdbSort sort) {
		begin = configBegin(begin);
		end = configEnd(end);

		AggregateInfluxdbQuery query = new AggregateInfluxdbQuery(new BasicInfluxdbQuery(influxdbProperties.getBucket(), configTableName(sensorData), LIMIT, begin, end));
		for (String column : configTagColumn()) {
			if (Func.isEmpty(sensorData.get(column))) {
				continue;
			}
			query.addFilter(column, sensorData.get(column).toString(), "==");
		}

		String sql = query.count();
		log.info("当前查询语句 ：{}", sql);
		List<FluxTable> table = influxDBClient.getQueryApi().query(sql);
		if (!table.isEmpty()) {
			Object _v = table.get(0).getRecords().get(0).getValueByKey("_value");
			page.setTotal(Long.parseLong(String.valueOf(_v)));
		}
		if (sort != null) {
			sort.sort(query);
		}
		long size = page.getSize();
		long current = page.getCurrent();
		query.setPage((int) size, (int) current);
		return fillData(query);
	}

	private List<JSONObject> fillData(InfluxdbQuerySQL query) {
		String sql = query.createQuerySQL();
		log.info("当前语句：{}", sql);
		List<JSONObject> list = new ArrayList<>();
		Map<Long, Integer> map = new HashMap<>();
		List<FluxTable> tables = influxDBClient.getQueryApi().query(sql);
		//标记当前时间顺序
		int flag = 0;
		for (FluxTable data : tables) {
			List<FluxRecord> records = data.getRecords();
			for (FluxRecord record : records) {
				Instant instant = record.getTime();
				if (instant == null) {
					continue;
				}
				Integer x = map.get(instant.toEpochMilli());
				if (x == null) {
					x = flag;
					map.put(record.getTime().toEpochMilli(), flag++);
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("originTime", record.getTime());
					String m = record.getMeasurement();
					assert m != null;
					if (m.contains("_1")) {
						m = m.substring(0, m.length() - 2);
					}
					jsonObject.put("waveId", m);
					list.add(jsonObject);
				}
				for (String tag : this.configTagColumn()) {
					Object obj = record.getValueByKey(tag);
					if (obj == null) {
						continue;
					}
					list.get(x).put(tag, obj);
				}
				list.get(x).put(record.getField(), record.getValue());
			}
		}
		return list;
	}

	/**
	 * 执行删除操作
	 *
	 * @param table  表名
	 * @param object 条件
	 * @param time   时间
	 * @return 是否删除成功
	 */
	public boolean deleteData(String table, JSONObject object, Long time) {
		Instant instant = Instant.ofEpochMilli(time);
		DeletePredicateRequest delete = new DeletePredicateRequest();
		ZoneOffset zoneOffset = ZoneOffset.ofHours(8);
		delete.start(instant.atOffset(zoneOffset))
			.stop(instant.plusMillis(1).atOffset(zoneOffset));
		StringBuffer pre = new StringBuffer();
		String[] config = configTagColumn();
		for (String column : config) {
			if (Func.isEmpty(object.get(column))) {
				continue;
			}
			pre.append(String.format("%s=\"%s\" and ", column, object.get(column)));
		}
		pre.append(String.format("_measurement=\"%s\" ", table));
		log.info("当前删除语句:{}", pre);
		delete.predicate(pre.toString());
		influxDBClient.getDeleteApi().delete(delete,
			influxdbProperties.getBucket(),
			influxdbProperties.getOrg());
		return true;
	}

}
