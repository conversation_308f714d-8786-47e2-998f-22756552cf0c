<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.message.mapper.MessageSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="messageSettingResultMap" type="com.snszyk.message.entity.MessageSetting">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="biz_type" property="bizType"/>
        <result column="send_strategy" property="sendStrategy"/>
        <result column="fixed_send_time" property="fixedSendTime"/>
        <result column="check_interval" property="checkInterval"/>
        <result column="interval_unit" property="intervalUnit"/>
        <result column="receiver_type" property="receiverType"/>
        <result column="receiver_info" property="receiverInfo"/>
        <result column="channel" property="channel"/>
        <result column="enabled" property="enabled"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 自定义分页查询 -->
    <select id="selectMessageSettingPage" resultType="com.snszyk.message.dto.MessageSettingDTO">
        SELECT
            id,
            tenant_id,
            biz_type,
            send_strategy,
            fixed_send_time,
            check_interval,
            interval_unit,
            receiver_type,
            receiver_info,
            channel,
            enabled,
            create_time,
            update_time
        FROM
            szyk_message_setting
        WHERE
            is_deleted = 0
        <if test="messageSetting.bizType != null and messageSetting.bizType != ''">
            AND biz_type = #{messageSetting.bizType}
        </if>
        <if test="messageSetting.sendStrategy != null and messageSetting.sendStrategy != ''">
            AND send_strategy = #{messageSetting.sendStrategy}
        </if>
        <if test="messageSetting.enabled != null">
            AND enabled = #{messageSetting.enabled}
        </if>
        ORDER BY
            create_time DESC
    </select>
</mapper>