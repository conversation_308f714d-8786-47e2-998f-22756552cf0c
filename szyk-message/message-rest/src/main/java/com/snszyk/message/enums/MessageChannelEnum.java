package com.snszyk.message.enums;

import lombok.Getter;

@Getter
public enum MessageChannelEnum {
	/**
	 * 小程序
	 */
	MINI_PROGRAM("MINI_PROGRAM", "小程序"),
	/**
	 * pc
	 */
	PC("PC", "pc"),

	;
	private String code;
	private String message;

	MessageChannelEnum(String code, String message) {
		this.code = code;
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}

	/**
	 * 根据code获取枚举类型
	 *
	 * @param code code
	 * @return
	 */
	public static MessageChannelEnum getByCode(String code) {
		for (MessageChannelEnum value : MessageChannelEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return null;
	}
}
