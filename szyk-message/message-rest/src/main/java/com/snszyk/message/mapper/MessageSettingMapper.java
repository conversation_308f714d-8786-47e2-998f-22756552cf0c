package com.snszyk.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.message.dto.MessageSettingDTO;
import com.snszyk.message.entity.MessageSetting;
import com.snszyk.message.vo.MessageSettingVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息设置表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface MessageSettingMapper extends BaseMapper<MessageSetting> {

    /**
     * 自定义分页
     *
     * @param page 分页参数
     * @param messageSetting 查询参数
     * @return 分页数据
     */
    List<MessageSettingDTO> selectMessageSettingPage(IPage<MessageSettingDTO> page, @Param("messageSetting") MessageSettingVO messageSetting);
    
    /**
     * 根据业务类型获取消息设置
     *
     * @param bizType 业务类型
     * @return 消息设置
     */
    MessageSettingDTO selectByBizType(@Param("bizType") String bizType);
}