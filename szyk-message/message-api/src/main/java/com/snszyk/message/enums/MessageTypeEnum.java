package com.snszyk.message.enums;

/**
 * 消息类型
 *
 * <AUTHOR>
 * @create 2022/8/1
 */
public enum MessageTypeEnum {

	/**
	 * 应用内消息
	 */
	IN_APP("IN_APP", "应用内消息"),

	/**
	 * PUSH
	 */
	PUSH("PUSH", "推送消息"),

	/**
	 * SMS
	 */
	SMS("SMS", "短信"),

	/**
	 * 邮箱
	 */
	EMAIL("EMAIL", "邮箱"),


	/**
	 * 弹窗浮层
	 */
	POP_UP("POP_UP", "弹窗浮层"),

	/**
	 * @我的
	 */
    AT_ME("AT_ME", "@我的"),

	/**
	 * 系统消息
	 */
    SYSTEM("SYSTEM", "系统消息"),

	/**
	 * 提示消息
	 */
	TIP("TIP", "提示消息"),

	/**
	 * 评论
	 */
    COMMENT("COMMENT", "评论"),

	/**
	 * 其它
	 */
    OTHER("OTHER", "其它"),

	/**
	 * 工作待办
	 */
    WORK_TODO("WORK_TODO", "工作待办"),

	/**
	 * 预警
	 */
	WARNING("WARNING", "预警"),

	/**
	 * 钉钉
	 */
    DING("DING", "钉钉"),

	/**
	 * 通知
	 */
    NOTICE("NOTICE", "通知");

    private String code;
    private String message;

    MessageTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

	/**
	 * 根据code获取枚举类型
	 * @param code code
	 * @return
	 */
	public static MessageTypeEnum getByCode(String code) {
		for (MessageTypeEnum value : MessageTypeEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return null;
	}
}
