package com.snszyk.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 未读消息数
 * <AUTHOR>
 */
@Data
@ApiModel
public class UnreadMessageCountDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 未读消息总数
	 */
	@ApiModelProperty("未读消息总数")
	private Integer totalCount;

	/**
	 * 未读待办消息数
	 */
	@ApiModelProperty("未读待办消息数")
	private Integer workToDoCount;

	/**
	 * 未读设备报警消息数
	 */
	@ApiModelProperty("未读设备报警消息数")
	private Integer equipmentAlarmCount;

	/**
	 * 未读采集站离线消息数
	 */
	@ApiModelProperty("未读采集站离线消息数")
	private Integer collectionStationOfflineCount;
}
