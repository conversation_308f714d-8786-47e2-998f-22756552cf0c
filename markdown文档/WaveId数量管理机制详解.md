# WaveId数量管理机制详解

## 1. 核心结论

❌ **WaveId在系统内的数量不是固定的**

✅ **WaveId是动态生成的，随传感器绑定自动创建**

✅ **不需要手动添加，系统会根据传感器配置自动生成**

## 2. WaveId的生成机制

### 2.1 自动生成流程

```mermaid
flowchart TD
    A[传感器实例绑定到监测点] --> B[系统检测新绑定的传感器]
    B --> C[获取传感器参数配置]
    C --> D[根据传感器类型生成Wave]
    D --> E[保存到数据库]
    E --> F[生成对应的报警门限]
    
    A1[用户操作] --> B1[调用bindSensorInstance接口]
    B1 --> C1[SensorInstanceServiceImpl.bindSensorInstance]
    C1 --> D1[generateWave方法]
    D1 --> E1[waveService.saveBatch]
    E1 --> F1[generateThreshold方法]
```

### 2.2 生成触发条件

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/impl/SensorInstanceServiceImpl.java" mode="EXCERPT">
````java
@Override
public boolean bindSensorInstance(Long monitorId, List<SensorInstanceVO> sensorInstanceList) {
    if (Func.isNotEmpty(sensorInstanceList)) {
        // 只保留新增的 -> 生成波形 & 门限
        List<SensorInstanceVO> addedSensorInstanceList = sensorInstanceList.stream()
            .filter(sensorInstanceVO -> Func.isEmpty(sensorInstanceVO.getMonitorId()))
            .collect(Collectors.toList());
        
        if (Func.isEmpty(addedSensorInstanceList)) {
            log.info("暂无新增绑定的传感器实例，无需生成波形和门限！");
            return true;
        }

        // 生成波形 - 关键步骤
        Map<Long, Long> waveMap = generateWave(monitorId, addedSensorInstanceList);

        // 生成报警门限
        generateThreshold(monitorId, addedSensorInstanceList, waveMap);
    }
    return true;
}
````
</augment_code_snippet>

## 3. Wave生成的详细逻辑

### 3.1 generateWave方法

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/impl/SensorInstanceServiceImpl.java" mode="EXCERPT">
````java
private Map<Long, Long> generateWave(Long monitorId, List<SensorInstanceVO> sensorInstanceList) {
    List<Wave> waveList = new ArrayList<>();
    Map<Long, Long> waveMap = new HashMap<>(16);
    
    // 1、温振一体传感器
    List<SensorInstanceVO> tempVibrateSensorList = sensorInstanceList.stream()
        .filter(s -> SensorCategoryEnum.TEMP_VIBRATE.getCode().equals(s.getCategory()))
        .collect(Collectors.toList());
    
    if (Func.isNotEmpty(tempVibrateSensorList)) {
        tempVibrateSensorList.forEach(tempVibrateSensor -> {
            // 振动 + 温度参数列表
            List<SensorInstanceParamVO> waveParamList = tempVibrateSensor.getSensorInstanceParamList().stream()
                .filter(paramVO -> paramVO.getVibrationType() == 1
                    || SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode()
                    .equals(paramVO.getSampleDataType()))
                .collect(Collectors.toList());
            
            if (Func.isNotEmpty(waveParamList)) {
                waveParamList.forEach(paramVO -> {
                    // 为每个传感器参数创建一个Wave
                    Wave wave = new Wave()
                        .setMonitorId(monitorId)
                        .setSensorCode(tempVibrateSensor.getCode())
                        .setSampleDataType(paramVO.getSampleDataType())
                        .setSensorInstanceParamId(paramVO.getId())
                        .setMeasureDirection(measureDirection)
                        .setCreateTime(DateUtil.now())
                        .setWaveName(getWaveName(sensorType, tempVibrateSensor, paramVO));
                    waveList.add(wave);
                });
            }
        });
    }
    
    // 2、应力波传感器
    // 3、电流传感器
    // 4、转速传感器
    // ... 其他传感器类型的处理逻辑
    
    // 批量保存生成的Wave
    if (Func.isNotEmpty(waveList)) {
        boolean saveWaveBatch = waveService.saveBatch(waveList);
        log.info("测点({})绑定传感器，生成了{}条波形：{}", monitorId, waveList.size(), saveWaveBatch);
    }
    
    return waveMap;
}
````
</augment_code_snippet>

## 4. 不同传感器类型的Wave生成规则

### 4.1 温振一体传感器

```java
// 一个温振一体传感器可能生成多个Wave
传感器实例: TEMP_VIBRATE_001
├── Wave1: X方向加速度 (sampleDataType: acceleration, measureDirection: 1)
├── Wave2: Y方向加速度 (sampleDataType: acceleration, measureDirection: 2)
├── Wave3: Z方向加速度 (sampleDataType: acceleration, measureDirection: 3)
├── Wave4: X方向速度 (sampleDataType: velocity, measureDirection: 1)
├── Wave5: Y方向速度 (sampleDataType: velocity, measureDirection: 2)
├── Wave6: Z方向速度 (sampleDataType: velocity, measureDirection: 3)
└── Wave7: 温度 (sampleDataType: temperature, measureDirection: 0)
```

### 4.2 应力波传感器

```java
// 应力波传感器根据编号生成Wave
传感器实例: STRESS_WAVE_001
├── Wave1: 应力波1号 (number: "1")
├── Wave2: 应力波2号 (number: "2")
└── Wave3: 应力波3号 (number: "3")
```

### 4.3 电流传感器

```java
// 电流传感器根据相位生成Wave
传感器实例: CURRENT_001
├── Wave1: A相电流 (phase: "A")
├── Wave2: B相电流 (phase: "B")
└── Wave3: C相电流 (phase: "C")
```

## 5. Wave数量的动态性

### 5.1 数量变化场景

1. **新增传感器**：绑定新传感器时自动生成对应的Wave
2. **传感器解绑**：解绑传感器时Wave被标记为解绑状态
3. **传感器重新配置**：修改传感器参数可能影响Wave数量

### 5.2 Wave的生命周期

```mermaid
stateDiagram-v2
    [*] --> 创建: 传感器绑定
    创建 --> 活跃: 正常使用
    活跃 --> 解绑: 传感器解绑
    活跃 --> 修改: 参数调整
    修改 --> 活跃: 更新完成
    解绑 --> [*]: 标记删除
```

### 5.3 解绑机制

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/impl/WaveServiceImpl.java" mode="EXCERPT">
````java
@Override
public int unbindWaveByIds(List<Long> waveIdList) {
    // 将Wave标记为解绑状态，而不是物理删除
    return baseMapper.unbindWaveByIds(waveIdList);
}
````
</augment_code_snippet>

## 6. 实际应用示例

### 6.1 风机监测点配置

```
风机设备 (Equipment)
├── 主轴承监测点 (Monitor)
│   ├── 温振一体传感器 TEMP_001
│   │   ├── WaveId: 1001 (X方向加速度)
│   │   ├── WaveId: 1002 (Y方向加速度)
│   │   ├── WaveId: 1003 (Z方向加速度)
│   │   ├── WaveId: 1004 (X方向速度)
│   │   ├── WaveId: 1005 (Y方向速度)
│   │   ├── WaveId: 1006 (Z方向速度)
│   │   └── WaveId: 1007 (温度)
│   └── 应力波传感器 STRESS_001
│       ├── WaveId: 1008 (应力波1号)
│       ├── WaveId: 1009 (应力波2号)
│       └── WaveId: 1010 (应力波3号)
├── 齿轮箱监测点 (Monitor)
│   └── 温振一体传感器 TEMP_002
│       ├── WaveId: 1011 (X方向加速度)
│       ├── WaveId: 1012 (Y方向加速度)
│       └── ... (更多Wave)
└── 发电机监测点 (Monitor)
    └── 电流传感器 CURRENT_001
        ├── WaveId: 1020 (A相电流)
        ├── WaveId: 1021 (B相电流)
        └── WaveId: 1022 (C相电流)
```

### 6.2 数量统计

```sql
-- 查询系统中的Wave总数
SELECT COUNT(*) FROM sidas_wave WHERE unbind = 0;

-- 按监测点统计Wave数量
SELECT monitor_id, COUNT(*) as wave_count 
FROM sidas_wave 
WHERE unbind = 0 
GROUP BY monitor_id;

-- 按传感器类型统计Wave数量
SELECT sample_data_type, COUNT(*) as wave_count 
FROM sidas_wave 
WHERE unbind = 0 
GROUP BY sample_data_type;
```

## 7. 系统配置影响

### 7.1 传感器参数配置

传感器的参数配置直接影响生成的Wave数量：

```java
// 传感器实例参数配置
SensorInstanceParam param1 = new SensorInstanceParam()
    .setSampleDataType("acceleration")  // 加速度
    .setAxialDirection(1)               // X轴
    .setVibrationType(1);               // 振动类型

SensorInstanceParam param2 = new SensorInstanceParam()
    .setSampleDataType("velocity")      // 速度
    .setAxialDirection(1)               // X轴
    .setVibrationType(1);               // 振动类型

// 每个参数配置会生成一个对应的Wave
```

### 7.2 设备类型影响

不同设备类型的标准配置会影响Wave的生成：

- **风机**：通常配置3轴振动 + 温度 = 7个Wave
- **水泵**：通常配置2轴振动 + 温度 = 5个Wave
- **压缩机**：可能包含应力波监测 = 更多Wave

## 8. 管理和维护

### 8.1 Wave的查询

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/impl/WaveServiceImpl.java" mode="EXCERPT">
````java
@Override
public WaveDTO getBy(Long id) {
    WaveDTO waveDTO = null;
    Wave wave = super.getById(id);
    if (Func.isNotEmpty(wave)) {
        waveDTO = Objects.requireNonNull(BeanUtil.copy(wave, WaveDTO.class));
        Monitor monitor = monitorMapper.selectById(wave.getMonitorId());
        waveDTO.setTenantId(monitor.getTenantId())
               .setEquipmentId(monitor.getEquipmentId())
               .setPath(monitor.getPath() + StringPool.COMMA + id)
               .setPathName(monitor.getPathName() + StringPool.COMMA + wave.getWaveName());
    }
    return waveDTO;
}
````
</augment_code_snippet>

### 8.2 Wave的状态管理

```java
// Wave实体中的关键状态字段
public class Wave {
    /**
     * 解绑状态：0-未解绑；1-已解绑
     */
    private Integer unbind;
    
    /**
     * 创建时间
     */
    private Date createTime;
}
```

## 9. 性能考虑

### 9.1 批量操作

系统采用批量操作来提高性能：

```java
// 批量保存Wave
boolean saveWaveBatch = waveService.saveBatch(waveList);

// 批量解绑Wave
int unbindWaveByIds(List<Long> waveIdList);
```

### 9.2 缓存机制

对于频繁访问的Wave信息，系统使用Redis缓存：

```java
// 停机线缓存Redis（只针对加速度波形）
if (SampledDataTypeEnum.ACCELERATION == SampledDataTypeEnum.getByCode(wave.getSampleDataType())) {
    String haltLineKey = String.format(HALT_LINE, wave.getSensorCode(), StringPool.COLON);
    szykRedis.set(haltLineKey, wave.getHaltLine());
}
```

## 10. 总结

### 10.1 关键特点

1. **动态生成**：WaveId随传感器绑定自动创建
2. **类型相关**：不同传感器类型生成不同数量的Wave
3. **参数驱动**：传感器参数配置决定Wave的具体属性
4. **状态管理**：支持解绑而非物理删除
5. **批量处理**：采用批量操作提高性能

### 10.2 数量规律

- **温振一体传感器**：通常生成6-7个Wave（3轴振动×2类型+温度）
- **应力波传感器**：根据编号数量生成对应Wave
- **电流传感器**：通常生成3个Wave（A、B、C三相）
- **转速传感器**：通常生成1个Wave

### 10.3 管理建议

1. **规划先行**：在设备配置阶段合理规划传感器布局
2. **标准化配置**：建立标准的传感器配置模板
3. **定期清理**：定期清理解绑的Wave记录
4. **监控数量**：监控Wave数量增长，避免过度膨胀

**结论**：WaveId的数量是动态的，完全由传感器配置驱动，系统会根据实际的传感器绑定情况自动生成和管理Wave，无需手动干预。这种设计确保了系统的灵活性和可扩展性。
