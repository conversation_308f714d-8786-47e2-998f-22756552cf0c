# WaveId详解与异常生成关系

## 1. WaveId的定义

**WaveId**是系统中传感器波形的唯一标识符，代表一个具体的传感器数据采集通道。

## 2. 数据层级关系

```mermaid
graph TD
    A[设备 Equipment] --> B[监测点 Monitor]
    B --> C[传感器实例 SensorInstance]
    C --> D[波形 Wave]
    D --> E[传感器数据 SensorData]
    
    A1[设备ID: equipmentId] --> B1[监测点ID: monitorId]
    B1 --> C1[传感器实例ID: sensorInstanceId]
    C1 --> D1[波形ID: waveId]
    D1 --> E1[采样数据]
```

### 2.1 层级说明

1. **设备（Equipment）**：一台物理设备，如风机、水泵等
2. **监测点（Monitor）**：设备上的一个监测部位，如轴承、齿轮箱等
3. **传感器实例（SensorInstance）**：安装在监测点上的具体传感器
4. **波形（Wave）**：传感器的一个数据采集通道
5. **传感器数据（SensorData）**：具体的采样数据

## 3. Wave实体结构

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/Wave.java" mode="EXCERPT">
````java
@Data
@TableName("sidas_wave")
@ApiModel(value = "传感器波形表", description = "传感器波形表")
public class Wave implements Serializable {
    
    /**
     * 主键 - 波形ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 波形名称
     */
    private String waveName;
    
    /**
     * 测点id
     */
    private Long monitorId;
    
    /**
     * 传感器实例编码
     */
    private String sensorCode;
    
    /**
     * 传感器实例参数id
     */
    private Long sensorInstanceParamId;
    
    /**
     * 采样数据类型
     */
    private String sampleDataType;
    
    /**
     * 测量方向
     */
    private Integer measureDirection;
    
    /**
     * 报警等级
     */
    private Integer alarmLevel;
    
    /**
     * 停机线
     */
    private BigDecimal haltLine;
}
````
</augment_code_snippet>

## 4. WaveId包含的信息

### 4.1 基本信息
- **波形名称（waveName）**：如"X方向振动"、"Y方向振动"、"温度"等
- **监测点ID（monitorId）**：所属的监测点
- **传感器编码（sensorCode）**：对应的传感器实例

### 4.2 数据特征
- **采样数据类型（sampleDataType）**：速度、加速度、位移、温度等
- **测量方向（measureDirection）**：X轴、Y轴、Z轴等
- **相位（phase）**：A相、B相、C相（电流传感器）
- **编号（number）**：应力波传感器的编号

### 4.3 报警配置
- **报警等级（alarmLevel）**：当前波形的报警等级
- **停机线（haltLine）**：停机阈值

## 5. 一个监测点的多个WaveId

### 5.1 典型配置示例

一个振动监测点通常包含多个WaveId：

```mermaid
graph TD
    A[监测点: 主轴承] --> B[WaveId_1: X方向加速度]
    A --> C[WaveId_2: Y方向加速度]
    A --> D[WaveId_3: Z方向加速度]
    A --> E[WaveId_4: X方向速度]
    A --> F[WaveId_5: Y方向速度]
    A --> G[WaveId_6: Z方向速度]
    A --> H[WaveId_7: 温度]
```

### 5.2 实际数据示例

```java
// 监测点：主轴承 (monitorId: 123456)
Wave wave1 = new Wave()
    .setId(1001L)
    .setWaveName("X方向加速度")
    .setMonitorId(123456L)
    .setSampleDataType("acceleration")
    .setMeasureDirection(1); // X轴

Wave wave2 = new Wave()
    .setId(1002L)
    .setWaveName("Y方向加速度")
    .setMonitorId(123456L)
    .setSampleDataType("acceleration")
    .setMeasureDirection(2); // Y轴

Wave wave3 = new Wave()
    .setId(1003L)
    .setWaveName("温度")
    .setMonitorId(123456L)
    .setSampleDataType("temperature")
    .setMeasureDirection(0); // 无方向
```

## 6. WaveId在异常生成中的作用

### 6.1 异常生成粒度

**一个WaveId可以生成一个异常详情（AbnormalDetail）**，但不是一个完整的设备异常。

```mermaid
graph TD
    A[设备异常 Abnormal] --> B[异常详情1 AbnormalDetail]
    A --> C[异常详情2 AbnormalDetail]
    A --> D[异常详情3 AbnormalDetail]
    
    B --> B1[WaveId: 1001]
    B --> B2[异常记录 AbnormalRecord]
    
    C --> C1[WaveId: 1002]
    C --> C2[异常记录 AbnormalRecord]
    
    D --> D1[WaveId: 1003]
    D --> D2[异常记录 AbnormalRecord]
```

### 6.2 异常生成逻辑

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
public void alarmDataHandler(String tenantId, Long waveId, Date originTime) {
    // 1. 根据WaveId获取波形信息
    WaveDTO waveDTO = waveService.getBy(waveId);
    
    // 2. 检查设备是否已存在故障状态的异常
    Abnormal abnormal = abnormalService.getOne(Wrappers.<Abnormal>query().lambda()
        .eq(Abnormal::getEquipmentId, waveDTO.getEquipmentId())
        .eq(Abnormal::getStatus, AbnormalStatusEnum.IS_FAULT.getCode()));
    
    // 3. 计算该WaveId的权重
    // 4. 如果权重达到阈值，生成异常详情
    if (weightSum >= strategy.getWeightSum()) {
        AbnormalDetail abnormalDetail = new AbnormalDetail()
            .setWaveId(waveId)
            .setMonitorId(waveDTO.getMonitorId())
            .setWaveName(waveDTO.getWaveName());
    }
}
````
</augment_code_snippet>

## 7. 异常数据结构关系

### 7.1 数据表关系

```mermaid
erDiagram
    EQUIPMENT ||--o{ MONITOR : "包含"
    MONITOR ||--o{ WAVE : "包含"
    WAVE ||--o{ SENSOR_DATA : "产生"
    
    EQUIPMENT ||--o{ ABNORMAL : "产生"
    ABNORMAL ||--o{ ABNORMAL_DETAIL : "包含"
    ABNORMAL_DETAIL ||--o{ ABNORMAL_RECORD : "包含"
    
    WAVE ||--|| ABNORMAL_DETAIL : "对应"
    WAVE ||--|| ABNORMAL_RECORD : "关联"
    
    EQUIPMENT {
        Long id "设备ID"
        String name "设备名称"
    }
    
    MONITOR {
        Long id "监测点ID"
        Long equipmentId "设备ID"
        String name "监测点名称"
    }
    
    WAVE {
        Long id "波形ID"
        Long monitorId "监测点ID"
        String waveName "波形名称"
        String sampleDataType "数据类型"
    }
    
    ABNORMAL {
        Long id "异常ID"
        Long equipmentId "设备ID"
        Integer abnormalLevel "异常等级"
    }
    
    ABNORMAL_DETAIL {
        Long id "异常详情ID"
        Long abnormalId "异常ID"
        Long waveId "波形ID"
        Long monitorId "监测点ID"
    }
    
    ABNORMAL_RECORD {
        Long id "异常记录ID"
        Long abnormalId "异常ID"
        Long waveId "波形ID"
        Integer abnormalType "异常类型"
    }
```

### 7.2 异常生成规则

1. **设备级异常（Abnormal）**：
   - 一个设备只能有一个未关闭的异常
   - 异常等级取所有异常详情的最高等级

2. **波形级异常详情（AbnormalDetail）**：
   - 一个WaveId对应一个异常详情
   - 记录具体的波形异常信息

3. **异常记录（AbnormalRecord）**：
   - 记录具体的异常原因（门限、机理、AI）
   - 一个异常详情可以有多个异常记录

## 8. 实际应用场景

### 8.1 单波形异常

```java
// 场景：只有X方向振动超限
WaveId: 1001 (X方向加速度) -> 生成异常详情1
设备异常等级 = 异常详情1的等级
```

### 8.2 多波形异常

```java
// 场景：X、Y方向振动都超限
WaveId: 1001 (X方向加速度) -> 生成异常详情1 (等级2)
WaveId: 1002 (Y方向加速度) -> 生成异常详情2 (等级3)
设备异常等级 = max(2, 3) = 3
```

### 8.3 复合异常

```java
// 场景：振动超限 + 温度异常
WaveId: 1001 (X方向加速度) -> 异常详情1 (门限报警 + 机理报警)
WaveId: 1003 (温度) -> 异常详情2 (门限报警)
```

## 9. 关键特点总结

### 9.1 WaveId的特点

1. **唯一性**：每个WaveId代表一个独特的数据采集通道
2. **层级性**：属于特定的监测点和设备
3. **多样性**：同一监测点可有多个不同类型的WaveId
4. **独立性**：每个WaveId独立进行异常判断

### 9.2 异常生成特点

1. **波形粒度**：异常生成以WaveId为最小单位
2. **设备聚合**：同一设备的多个WaveId异常会聚合为一个设备异常
3. **等级继承**：设备异常等级取所有波形异常的最高等级
4. **状态同步**：设备异常状态影响所有相关的波形异常

### 9.3 数据流转

```
传感器数据 -> WaveId -> 报警判断 -> Redis标志位 -> 二次确认 -> 异常生成
```

**结论**：一个WaveId不能直接生成一个完整的设备异常，但可以生成一个异常详情。多个WaveId的异常详情会聚合成一个设备级的异常记录。
