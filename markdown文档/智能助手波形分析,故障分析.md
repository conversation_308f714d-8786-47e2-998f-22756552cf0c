# SmartController 中 processRequest 方法的故障分析和波形分析处理逻辑

## 1. 概述

`SmartController` 类中的 `processRequest` 方法是一个核心方法，它处理客户端的智能对话请求，并根据请求参数执行不同的处理逻辑，包括波形分析和故障诊断。该方法通过 Server-Sent Events (SSE) 技术实现实时响应流，将 AI 处理结果推送给客户端。

本文将详细分析该方法中的波形分析和故障诊断处理逻辑，包括数据流、处理步骤和关键技术实现，并结合实际的 REST 请求参数进行说明。

## 2. 方法签名和基本结构

```java
@PostMapping(path = "/process/request", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
@ApiOperationSupport(order = 1)
@ApiOperation(value = "dify请求接口", notes = "传入MessageVo")
public SseEmitter processRequest(@RequestBody MessageVo v) {
    // 波形分析逻辑
    if (v.getMonitorId() != null) {
        // ...波形分析处理...
    }
    
    // 故障诊断逻辑
    if (v.getEquipmentId() != null) {
        // ...故障诊断处理...
    }
    
    // 调用 AI 服务并返回 SSE 响应
    return smartService.processRequest(v);
}
```

该方法接收 `MessageVo` 类型的请求体，根据请求中是否包含 `monitorId` 或 `equipmentId` 来决定执行波形分析或故障诊断逻辑，最后调用 `smartService.processRequest(v)` 处理请求并返回 SSE 响应。

## 3. 波形分析处理逻辑

### 3.1 触发条件与请求参数

当请求中包含 `monitorId`（监测点ID）时，执行波形分析逻辑。以下是波形分析的实际 REST 请求参数示例：

```json
{
    "content": "",
    "inputs": {
        "monitorId": "1813381661698621441",
        "picFile": {
            "type": "image",
            "transfer_method": "remote_url",
            "url": "https://zd.allfinetech.cn/upload/file/20250514/d450df96-90af-4726-8620-d02dd93c0c6f.jpg"
        }
    },
    "monitorId": "1813381661698621441",
    "response_mode": "streaming",
    "user": "1615153141960740865",
    "conversationId": "",
    "type": "workflow-wave-auth"
}
```

波形分析的处理代码：

```java
if (v.getMonitorId() != null) {
    //波形分析
    String equipmentInfo = diagnosisService.getEquipmentByMonitorId(v.getMonitorId());
    Map<String, Object> inputs = v.getInputs();
    if (inputs != null) {
        inputs.put("equipment", equipmentInfo);
    }
}
```

从请求参数可以看出，波形分析请求包含以下关键信息：
- `monitorId`：监测点ID，用于获取相关设备信息
- `picFile`：波形图片文件，包含类型、传输方式和URL
- `type`：请求类型为 "workflow-wave-auth"，表示这是一个波形分析工作流请求
- `response_mode`：响应模式为 "streaming"，表示使用流式响应

### 3.2 处理流程

1. **获取设备信息**：
   - 调用 `diagnosisService.getEquipmentByMonitorId(v.getMonitorId())` 获取与监测点关联的设备信息
   - 该方法内部通过 `iEquipmentService.getEquipmentByMonitorId(monitorId)` 查询设备信息，并将其转换为 LLM 提示格式

2. **更新输入参数**：
   - 获取请求中的 `inputs` 参数映射
   - 将设备信息添加到 `inputs` 参数中，键为 "equipment"
   - 这些参数将作为 AI 处理的输入

### 3.3 关键方法实现

`DiagnosisService` 中的 `getEquipmentByMonitorId` 方法：

```java
public String getEquipmentByMonitorId(Long monitorId) {
    return Optional.ofNullable(iEquipmentService.getEquipmentByMonitorId(monitorId))
            .map(dto -> dto.convertLLmPrompt().toString())
            .orElse("");
}
```

`AiEquipmentDto` 中的 `convertLLmPrompt` 方法：

```java
public StringBuilder convertLLmPrompt(){
    StringBuilder b = new StringBuilder();
    b.append("1.设备信息：")
     .append("<br>")
     .append("&emsp;&emsp;设备名称：")
     .append(this.name)
     .append("，设备编码：")
     .append(this.code)
     .append("，设备型号：")
     .append(this.model)
     .append("，设备转速：")
     .append(this.rev)
     .append("，设备频率：")
     .append(this.frequency)
     .append("，设备功率：")
     .append(this.power);
    return b;
}
```

### 3.4 数据流图

```
客户端请求 (包含 monitorId)
    │
    ▼
SmartController.processRequest
    │
    ▼
DiagnosisService.getEquipmentByMonitorId
    │
    ▼
IEquipmentService.getEquipmentByMonitorId
    │
    ▼
AiEquipmentDto.convertLLmPrompt
    │
    ▼
更新 inputs 参数 (添加 equipment 信息)
    │
    ▼
smartService.processRequest (处理请求并返回 SSE 响应)
```

## 4. 故障诊断处理逻辑

### 4.1 触发条件与请求参数

当请求中包含 `equipmentId`（设备ID）时，执行故障诊断逻辑。以下是故障诊断的实际 REST 请求参数示例：

```json
{
    "content": "电机不能启动",
    "inputs": {
        "equipmentId": "1813381116212609026",
        "desc": "电机不能启动"
    },
    "equipmentId": "1813381116212609026",
    "desc": "电机不能启动",
    "response_mode": "streaming",
    "user": "1615153141960740865",
    "conversationId": "",
    "type": "workflow-diagnosis-auth"
}
```

故障诊断的处理代码：

```java
if (v.getEquipmentId() != null) {
    try {
        long start = System.currentTimeMillis();
        diagnosisService.waveInfo(v.getEquipmentId(), v.getDesc(), v.getInputs());
        long end = System.currentTimeMillis();
        log.info("waveInfo耗时：{}s", (end - start) / 1000.0);
    } catch (IOException e) {
        throw new RuntimeException(e);
    }
}
```

从请求参数可以看出，故障诊断请求包含以下关键信息：
- `equipmentId`：设备ID，用于获取设备信息和相关监测点
- `desc`：故障描述，这里是 "电机不能启动"
- `content`：与 `desc` 相同，包含故障描述
- `type`：请求类型为 "workflow-diagnosis-auth"，表示这是一个故障诊断工作流请求
- `response_mode`：响应模式为 "streaming"，表示使用流式响应

### 4.2 处理流程

1. **记录开始时间**：
   - 使用 `System.currentTimeMillis()` 记录处理开始时间

2. **调用波形信息处理**：
   - 调用 `diagnosisService.waveInfo(v.getEquipmentId(), v.getDesc(), v.getInputs())` 处理波形信息
   - 传入设备ID、故障描述和输入参数

3. **记录处理耗时**：
   - 计算并记录处理耗时，便于性能监控

4. **异常处理**：
   - 捕获 `IOException` 并转换为 `RuntimeException` 抛出

### 4.3 waveInfo 方法详细分析

`DiagnosisService` 中的 `waveInfo` 方法是故障诊断的核心，它完成以下任务：

1. **获取设备信息**：
   ```java
   AiEquipmentDto equipment = iEquipmentService.getEquipmentInfo(equipmentId);
   StringBuilder equipmentStr = equipment.convertLLmPrompt();
   equipmentStr.append("<br>2.设备部位：<br>&emsp;&emsp;");
   ```

2. **获取监测点列表**：
   ```java
   List<AiMonitorDto> monitorList = iEquipmentService.getMonitorList(equipmentId);
   Map<String, AiMonitorDto> waveformMap = new HashMap<>();
   AtomicReference<BigDecimal> samplingFrequency = new AtomicReference<>(BigDecimal.ZERO);
   ```

3. **处理每个监测点的波形数据**：
   ```java
   monitorList.forEach(monitorDto -> {
       equipmentStr.append(monitorDto.getName()).append(",");
       processMonitorWaveforms(monitorDto, waveformMap, samplingFrequency);
   });
   ```

4. **添加故障描述**：
   ```java
   equipmentStr.append("<br>3.故障现象：").append(desc);
   ```

5. **构建波形提示**：
   ```java
   StringBuilder wavePrompt = new StringBuilder("波形图与部位对应关系：");
   ```

6. **处理波形数据**：
   ```java
   List<String> waveFiles = processWaveforms(new ArrayList<>(waveformMap.keySet()),
       waveformMap, samplingFrequency.get(), wavePrompt);
   ```

7. **更新输入参数**：
   ```java
   updateInputs(inputs, equipmentId, equipmentStr.toString(), waveFiles, wavePrompt);
   ```

### 4.4 processMonitorWaveforms 方法分析

该方法处理单个监测点的波形数据：

```java
private void processMonitorWaveforms(AiMonitorDto monitor, Map<String, AiMonitorDto> waveformMap,
                                     AtomicReference<BigDecimal> samplingFrequency) {
    // 获取波形列表
    List<AiWaveDto> waves = iEquipmentService.waveList(monitor.getId());
    if (CollectionUtils.isEmpty(waves)) {
        return;
    }

    // 获取最新波形
    AiWaveDto latestWave = waves.get(0);
    samplingFrequency.set(latestWave.getSamplingFreq());

    // 获取主波形数据
    JSONObject waveform = mainWaveForm(monitor.getId(), latestWave.getId());
    if (waveform != null && waveform.getString("waveformUrl") != null) {
        String waveformUrl = waveProperties.getFileUrl() + waveform.getString("waveformUrl");
        waveformMap.put(waveformUrl, monitor);
    }
}
```

### 4.5 processWaveforms 方法分析

该方法处理波形数据并获取波形文件列表：

```java
private List<String> processWaveforms(List<String> waveUrls, Map<String, AiMonitorDto> waveformMap,
                                      BigDecimal samplingFrequency, StringBuilder wavePrompt) throws IOException {
    // 记录开始时间
    long startTime = System.currentTimeMillis();
    
    // 调用波形处理服务
    String result = waveClient.post(
        waveProperties.getUrl() + waveProperties.getWaveRoute(),
        waveClient.buildJsonBody(waveUrls, samplingFrequency)
    );
    logExecutionTime(startTime);

    // 处理结果
    List<String> waveFiles = new ArrayList<>();
    if (result != null) {
        List<String> resultList = JSON.parseArray(result, String.class);
        int existPic = 0;
        for (int i = 0; i < resultList.size(); i++) {
            if (Func.isNotBlank(resultList.get(i))) {
                existPic++;
                AiMonitorDto dto = waveformMap.get(waveUrls.get(i));
                joinStr(wavePrompt, dto, existPic);
                waveFiles.add(resultList.get(i));
            }
        }
    }
    return waveFiles;
}
```

### 4.6 updateInputs 方法分析

该方法更新输入参数：

```java
private void updateInputs(Map<String, Object> inputs, Long equipmentId,
                          String equipmentInfo, List<String> waveFiles, StringBuilder wavePrompt) {
    inputs.put("equipment", equipmentInfo);
    inputs.put("equipmentId", String.valueOf(equipmentId));
    inputs.put("waveFiles", generateDifyFiles(waveFiles));
    inputs.put("wavePrompt", wavePrompt.toString());
}
```

### 4.7 mainWaveForm 方法分析

该方法获取主波形数据：

```java
public JSONObject mainWaveForm(Long monitorId, Long waveId) {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("monitorId", monitorId);
    jsonObject.put("waveId", waveId);
    jsonObject.put("invalid", "0");

    List<JSONObject> list = influxdbTools.queryDataTable(
        dateConvert(START_DATE),
        dateConvert(END_DATE),
        jsonObject,
        null,
        (sql) -> {
            sql.getQuerySQL().append(" |>sort(columns: [\"_time\"], desc: true) ")
                .append(" |>limit(n: 1)");
            sql.addSort("_time", false);
        },
        (item) -> {
            JSONObject object = new JSONObject();
            object.put("waveId", waveId);
            object.put("sampleDataType", item.getValueByKey("sampleDataType"));
            object.put("monitorId", item.getValueByKey("monitorId"));
            object.put("originTime", item.getTime().toEpochMilli());
            object.put("value", item.getValueByKey("value"));
            object.put("waveformUrl", item.getValueByKey("waveformUrl"));
            return object;
        });
    return list.isEmpty() ? null : list.get(0);
}
```

### 4.8 数据流图

```
客户端请求 (包含 equipmentId)
    │
    ▼
SmartController.processRequest
    │
    ▼
DiagnosisService.waveInfo
    │
    ├── 获取设备信息 (iEquipmentService.getEquipmentInfo)
    │
    ├── 获取监测点列表 (iEquipmentService.getMonitorList)
    │
    ├── 处理每个监测点的波形数据 (processMonitorWaveforms)
    │   │
    │   ├── 获取波形列表 (iEquipmentService.waveList)
    │   │
    │   └── 获取主波形数据 (mainWaveForm)
    │       │
    │       └── 查询 InfluxDB 数据 (influxdbTools.queryDataTable)
    │
    ├── 处理波形数据 (processWaveforms)
    │   │
    │   └── 调用波形处理服务 (waveClient.post)
    │
    └── 更新输入参数 (updateInputs)
        │
        └── 生成波形文件 JSON 数组 (generateDifyFiles)
    │
    ▼
smartService.processRequest (处理请求并返回 SSE 响应)
```

## 5. 关键技术实现

### 5.1 InfluxDB 时序数据查询

系统使用 InfluxDB 存储和查询波形数据：

```java
List<JSONObject> list = influxdbTools.queryDataTable(
    dateConvert(START_DATE),
    dateConvert(END_DATE),
    jsonObject,
    null,
    (sql) -> {
        sql.getQuerySQL().append(" |>sort(columns: [\"_time\"], desc: true) ")
            .append(" |>limit(n: 1)");
        sql.addSort("_time", false);
    },
    (item) -> {
        // 转换结果...
    });
```

### 5.2 OkHttp 客户端

系统使用 OkHttp 客户端调用外部波形处理服务：

```java
public String post(String url, String requestBody) throws IOException {
    log.info("在线模拟数据生成请求体，{}", requestBody);
    log.info("请求url，{}", url);
    Request request = new Request.Builder()
        .url(url)
        .headers(getHeaders())
        .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), requestBody))
        .build();

    try (Response response = CLIENT.newCall(request).execute()) {
        if (response.body() == null) {
            return null;
        }
        String result = response.body().string();
        log.info("在线模拟数据生成返回结果，{}", result);
        return result;
    }
}
```

### 5.3 Server-Sent Events (SSE)

系统使用 SSE 技术实现实时响应流：

```java
@PostMapping(path = "/process/request", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
public SseEmitter processRequest(@RequestBody MessageVo v) {
    // 处理逻辑...
    return smartService.processRequest(v);
}
```

### 5.4 外部配置

系统使用 `@ConfigurationProperties` 实现配置外部化：

```java
@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "wave")
public class WaveProperties {
    /**
     * 提取波形服务地址
     */
    private String url;
    /**
     * 提取波形特征服务地址
     */
    private String featureRoute;

    private String waveRoute;

    /**
     * 文件目录
     */
    private String fileUrl;
}
```

## 6. 数据结构

### 6.1 MessageVo

```java
@Data
@ApiModel("消息")
public class MessageVo {
    /**
     * 用户标识
     */
    @ApiModelProperty("用户标识")
    private String user;
    /**
     * 消息内容
     */
    @ApiModelProperty("消息内容")
    private String content;
    /**
     * 会话标识
     */
    @ApiModelProperty("会话标识")
    private String conversationId;
    /**
     * 请求类型
     */
    @ApiModelProperty("请求类型")
    private String type;
    /**
     * 请求参数
     */
    @ApiModelProperty("请求参数")
    private Map<String, Object> inputs = new HashMap<>();
    /**
     * 部位id
     */
    @ApiModelProperty("部位id")
    private Long monitorId;
    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long equipmentId;

    private String desc;
}
```

### 6.2 AiEquipmentDto

```java
@Data
public class AiEquipmentDto {
    private Long id;
    /**
     * 父主键
     */
    private Long deviceId;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 型号
     */
    private String model;
    /**
     * 频率
     */
    private BigDecimal frequency;
    /**
     * 转速
     */
    private BigDecimal rev;
    // ... 其他属性 ...

    public StringBuilder convertLLmPrompt(){
        // 转换为 LLM 提示格式
    }
}
```

### 6.3 AiMonitorDto

```java
@Data
public class AiMonitorDto {
    /**
     * 主键
     */
    private Long id;
    /**
     * 父主键
     */
    private Long equipmentId;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    // ... 其他属性 ...
}
```

### 6.4 AiWaveDto

```java
@Data
@Accessors(chain = true)
public class AiWaveDto implements Comparable<AiWaveDto> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 波形名称
     */
    private String waveName;
    /**
     * 测点id
     */
    private Long monitorId;
    /**
     * 采样频率（KHz）
     */
    private BigDecimal samplingFreq;
    // ... 其他属性 ...

    @Override
    public int compareTo(AiWaveDto other) {
        // 比较逻辑
    }
}
```

## 7. 请求参数与处理流程对比

### 7.1 波形分析请求参数

```json
{
    "content": "",
    "inputs": {
        "monitorId": "1813381661698621441",
        "picFile": {
            "type": "image",
            "transfer_method": "remote_url",
            "url": "https://zd.allfinetech.cn/upload/file/20250514/d450df96-90af-4726-8620-d02dd93c0c6f.jpg"
        }
    },
    "monitorId": "1813381661698621441",
    "response_mode": "streaming",
    "user": "1615153141960740865",
    "conversationId": "",
    "type": "workflow-wave-auth"
}
```

### 7.2 故障诊断请求参数

```json
{
    "content": "电机不能启动",
    "inputs": {
        "equipmentId": "1813381116212609026",
        "desc": "电机不能启动"
    },
    "equipmentId": "1813381116212609026",
    "desc": "电机不能启动",
    "response_mode": "streaming",
    "user": "1615153141960740865",
    "conversationId": "",
    "type": "workflow-diagnosis-auth"
}
```

### 7.3 处理流程对比

| 特性 | 波形分析 | 故障诊断 |
|------|---------|----------|
| 触发条件 | `monitorId` 不为空 | `equipmentId` 不为空 |
| 请求类型 | workflow-wave-auth | workflow-diagnosis-auth |
| 主要输入 | 监测点ID和波形图片 | 设备ID和故障描述 |
| 处理逻辑 | 获取设备信息并添加到输入参数 | 获取设备信息、监测点列表、波形数据，并构建诊断输入 |
| 处理复杂度 | 较低 | 较高（涉及多个数据源和外部服务调用） |
| 响应模式 | streaming | streaming |

## 8. 总结

`SmartController` 中的 `processRequest` 方法实现了一个复杂的智能对话处理流程，包括波形分析和故障诊断两个主要功能：

1. **波形分析**：
   - 当请求中包含 `monitorId` 时触发
   - 处理类型为 "workflow-wave-auth"
   - 接收监测点ID和波形图片作为输入
   - 获取与监测点关联的设备信息
   - 将设备信息添加到输入参数中

2. **故障诊断**：
   - 当请求中包含 `equipmentId` 时触发
   - 处理类型为 "workflow-diagnosis-auth"
   - 接收设备ID和故障描述作为输入
   - 获取设备信息和监测点列表
   - 处理每个监测点的波形数据
   - 调用外部波形处理服务
   - 构建包含设备信息、故障描述和波形图的诊断输入

这两个功能共同为 AI 模型提供了丰富的上下文信息，使其能够进行更准确的智能对话和故障诊断。系统通过 SSE 技术实现了实时响应流，为用户提供流畅的交互体验。

该方法的实现充分利用了现代 Java 技术栈，包括 Spring WebFlux、OkHttp、InfluxDB 等，展示了一个复杂的工业 AI 应用的设计和实现。
