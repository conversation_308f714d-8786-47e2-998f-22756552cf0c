# 通道概念详解

## 1. 通道的定义

在AI运维平台中，**"通道"**主要指的是**采集站通道（Collection Station Channel）**，它是数据采集系统中的一个重要概念。

## 2. 通道的层级关系

```mermaid
graph TD
    A[采集站 CollectionStation] --> B[通道1 Channel]
    A --> C[通道2 Channel]
    A --> D[通道N Channel]
    
    B --> E[传感器实例 SensorInstance]
    C --> F[传感器实例 SensorInstance]
    D --> G[传感器实例 SensorInstance]
    
    E --> H[波形1 Wave]
    E --> I[波形2 Wave]
    F --> J[波形3 Wave]
    G --> K[波形4 Wave]
    
    A1[采集站: 8通道采集器] --> B1[通道1: 绑定TEMP_001]
    A1 --> C1[通道2: 绑定STRESS_001]
    A1 --> D1[通道3: 绑定CURRENT_001]
    A1 --> E1[通道4-8: 空闲]
```

## 3. 采集站通道实体结构

### 3.1 CollectionStationChannel实体

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/CollectionStationChannel.java" mode="EXCERPT">
````java
@TableName("eolm_collection_station_channel")
public class CollectionStationChannel implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 采集站id - 所属采集站
     */
    private Long stationId;

    /**
     * 名称 - 通道名称
     */
    private String name;

    /**
     * 是否在线：0-离线；1-在线
     */
    private Integer online;

    /**
     * 绑定的传感器编码 - 关键关联字段
     */
    private String sensorCode;
}
````
</augment_code_snippet>

### 3.2 CollectionStation实体

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/CollectionStation.java" mode="EXCERPT">
````java
@TableName("eolm_collection_station")
public class CollectionStation extends TenantEntity {
    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否在线：0-离线；1-在线
     */
    private Integer online;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 通道数 - 该采集站支持的通道总数
     */
    private Integer channelCount;

    /**
     * 厂家
     */
    private String manufacturer;

    /**
     * 有线无线（0：有线；1：无线）
     */
    private Integer isWireless;
}
````
</augment_code_snippet>

## 4. 通道的作用和功能

### 4.1 物理层面

1. **硬件接口**：采集站的物理接口，用于连接传感器
2. **数据采集**：每个通道负责采集一个传感器的数据
3. **信号转换**：将模拟信号转换为数字信号
4. **数据传输**：将采集的数据通过网络传输到系统

### 4.2 逻辑层面

1. **传感器绑定**：一个通道绑定一个传感器实例
2. **状态监控**：监控通道的在线/离线状态
3. **数据路由**：确定数据的来源和去向
4. **资源管理**：管理采集站的通道资源

## 5. 通道与其他概念的区别

### 5.1 通道 vs 波形

| 概念 | 定义 | 数量关系 | 作用 |
|------|------|----------|------|
| **通道 Channel** | 采集站的物理接口 | 1个通道 → 1个传感器 | 硬件连接和数据采集 |
| **波形 Wave** | 传感器的数据采集通道 | 1个传感器 → 多个波形 | 数据类型区分和存储 |

### 5.2 关系示例

```
采集站通道1 (Channel)
├── 绑定传感器: TEMP_001 (温振一体传感器)
    ├── 波形1 (Wave): X方向加速度
    ├── 波形2 (Wave): Y方向加速度
    ├── 波形3 (Wave): Z方向加速度
    ├── 波形4 (Wave): X方向速度
    ├── 波形5 (Wave): Y方向速度
    ├── 波形6 (Wave): Z方向速度
    └── 波形7 (Wave): 温度

采集站通道2 (Channel)
├── 绑定传感器: STRESS_001 (应力波传感器)
    ├── 波形1 (Wave): 应力波1号
    ├── 波形2 (Wave): 应力波2号
    └── 波形3 (Wave): 应力波3号
```

## 6. 通道的绑定机制

### 6.1 传感器与通道绑定

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/logic/CollectionStationLogicService.java" mode="EXCERPT">
````java
// 保存通道
channelList.forEach(channelVO -> channelVO.setStationId(collectionStation.getId()));
List<CollectionStationChannel> stationChannelList = BeanUtil.copy(channelList, CollectionStationChannel.class);
boolean saveChannels = stationChannelService.saveBatch(stationChannelList);

// 保存传感器实例与采集站的绑定状态
List<String> sensorCodeList = channelList.stream()
    .map(CollectionStationChannel::getSensorCode)
    .filter(Func::isNotEmpty)
    .collect(Collectors.toList());

if (CollectionUtil.isNotEmpty(sensorCodeList)) {
    int bindCount = sensorInstanceService.bindCollectionStation(sensorCodeList, collectionStation.getId());
    log.info("绑定传感器实例与采集站的绑定状态，绑定数量：{}", bindCount);
}
````
</augment_code_snippet>

### 6.2 通道查询逻辑

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/mapper/CollectionStationChannelMapper.xml" mode="EXCERPT">
````xml
<select id="selectChannelList" resultType="com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO">
    SELECT
        csc.id,
        csc.station_id,
        csc.`name`,
        csc.`online`,
        si.`code` AS sensor_code,
        sip.id AS spower_sensor_instance_param_id,
        REPLACE ( CONCAT( m.path_name, ',', st.`name` ), ',', '/' ) AS path_name,
        st.`name` AS sensor_name,
        st.is_wireless
    FROM
        eolm_collection_station_channel csc
    LEFT JOIN basic_sensor_instance si ON csc.sensor_code = si.CODE AND si.is_deleted = 0
    LEFT JOIN basic_sensor_instance_param sip ON si.id = sip.instance_id AND sip.sample_data_type = 'SPOWER'
    LEFT JOIN eolm_monitor m ON si.monitor_id = m.id
    LEFT JOIN basic_sensor_type st ON si.type_id = st.id
    WHERE
        csc.station_id = #{stationId}
</select>
````
</augment_code_snippet>

## 7. 通道状态管理

### 7.1 在线状态监控

```java
// 通道在线状态更新逻辑
public void updateChannelOnlineStatus(Long channelId, Integer online) {
    CollectionStationChannel channel = new CollectionStationChannel()
        .setId(channelId)
        .setOnline(online);
    channelService.updateById(channel);
}

// 采集站在线状态基于通道状态计算
public void updateStationOnlineStatus(Long stationId) {
    List<CollectionStationChannel> channels = channelService.list(
        Wrappers.<CollectionStationChannel>query().lambda()
            .eq(CollectionStationChannel::getStationId, stationId));
    
    long onlineChannelCount = channels.stream()
        .filter(channel -> channel.getOnline() == 1)
        .count();
    
    // 如果有通道在线，则采集站在线
    Integer stationOnline = onlineChannelCount > 0 ? 1 : 0;
    stationService.updateOnlineStatus(stationId, stationOnline);
}
```

### 7.2 电量监控

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/impl/CollectionStationServiceImpl.java" mode="EXCERPT">
````java
// 获取通道的电量信息
if (Func.isNotEmpty(channelDTO.getSpowerSensorInstanceParamId())) {
    String key = channelDTO.getSensorCode() + StringPool.COLON 
        + channelDTO.getSpowerSensorInstanceParamId() + StringPool.COLON 
        + SampledDataTypeEnum.SENSOR_POWER.getCode();
    channelDTO.setElectricity(szykRedis.get(key));
}
````
</augment_code_snippet>

## 8. 实际应用场景

### 8.1 风机监测系统配置

```
采集站: 风机1号采集器 (8通道)
├── 通道1: 主轴承温振传感器 (TEMP_001)
├── 通道2: 齿轮箱温振传感器 (TEMP_002)
├── 通道3: 发电机温振传感器 (TEMP_003)
├── 通道4: 主轴应力波传感器 (STRESS_001)
├── 通道5: 发电机电流传感器 (CURRENT_001)
├── 通道6: 转速传感器 (SPEED_001)
├── 通道7: 空闲
└── 通道8: 空闲
```

### 8.2 数据流转过程

```mermaid
sequenceDiagram
    participant S as 传感器
    participant C as 采集站通道
    participant CS as 采集站
    participant MQ as RabbitMQ
    participant SYS as 系统
    
    S->>C: 模拟信号
    C->>CS: 数字化数据
    CS->>MQ: 网络传输(sensorCode)
    MQ->>SYS: 消息队列
    SYS->>SYS: 根据sensorCode查找Wave
    SYS->>SYS: 数据存储和处理
```

## 9. 通道配置管理

### 9.1 通道容量规划

```java
// 采集站通道容量检查
public boolean checkChannelCapacity(Long stationId, List<String> newSensorCodes) {
    CollectionStation station = stationService.getById(stationId);
    List<CollectionStationChannel> existingChannels = channelService.list(
        Wrappers.<CollectionStationChannel>query().lambda()
            .eq(CollectionStationChannel::getStationId, stationId)
            .isNotNull(CollectionStationChannel::getSensorCode));
    
    int usedChannels = existingChannels.size();
    int newChannels = newSensorCodes.size();
    int totalChannels = station.getChannelCount();
    
    return (usedChannels + newChannels) <= totalChannels;
}
```

### 9.2 通道资源分配

```java
// 自动分配空闲通道
public List<CollectionStationChannel> allocateChannels(Long stationId, List<String> sensorCodes) {
    List<CollectionStationChannel> freeChannels = channelService.list(
        Wrappers.<CollectionStationChannel>query().lambda()
            .eq(CollectionStationChannel::getStationId, stationId)
            .isNull(CollectionStationChannel::getSensorCode));
    
    List<CollectionStationChannel> allocatedChannels = new ArrayList<>();
    for (int i = 0; i < sensorCodes.size() && i < freeChannels.size(); i++) {
        CollectionStationChannel channel = freeChannels.get(i);
        channel.setSensorCode(sensorCodes.get(i));
        allocatedChannels.add(channel);
    }
    
    return allocatedChannels;
}
```

## 10. 通道与消息通道的区别

### 10.1 采集站通道 vs 消息通道

| 类型 | 作用域 | 功能 | 示例 |
|------|--------|------|------|
| **采集站通道** | 数据采集层 | 硬件接口，传感器数据采集 | 8通道采集器的通道1 |
| **消息通道** | 应用层 | 消息推送渠道 | 小程序、PC端 |

<augment_code_snippet path="szyk-message/message-rest/src/main/java/com/snszyk/message/enums/MessageChannelEnum.java" mode="EXCERPT">
````java
@Getter
public enum MessageChannelEnum {
    /**
     * 小程序
     */
    MINI_PROGRAM("MINI_PROGRAM", "小程序"),
    /**
     * pc
     */
    PC("PC", "pc");
}
````
</augment_code_snippet>

## 11. 总结

### 11.1 通道的核心特点

1. **物理接口**：采集站的硬件接口，用于连接传感器
2. **一对一绑定**：一个通道绑定一个传感器实例
3. **状态监控**：支持在线/离线状态监控
4. **资源管理**：有限的通道资源需要合理分配
5. **数据路由**：确定数据的采集来源

### 11.2 通道在系统中的作用

- **硬件抽象**：将物理硬件抽象为逻辑概念
- **资源管理**：管理有限的采集站通道资源
- **状态监控**：监控硬件设备的在线状态
- **数据路由**：确定数据采集的物理路径
- **配置管理**：支持传感器的动态绑定和解绑

**结论**：通道是数据采集系统中的重要概念，它代表采集站的物理接口，负责连接传感器并采集数据。一个通道绑定一个传感器，而一个传感器可以产生多个波形数据。通道是硬件层面的概念，波形是软件层面的数据分类概念。
