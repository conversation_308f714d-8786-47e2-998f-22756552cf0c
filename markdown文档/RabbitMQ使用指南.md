# RabbitMQ使用指南

## 1. RabbitMQ简介

RabbitMQ是一个开源的消息代理和队列服务器，用来通过普通协议在完全不同的应用之间共享数据。RabbitMQ是使用Erlang语言来编写的，并且基于AMQP（Advanced Message Queuing Protocol）协议。

### 1.1 核心概念

- **Producer（生产者）**：发送消息的应用程序
- **Consumer（消费者）**：接收消息的应用程序
- **Queue（队列）**：存储消息的缓冲区
- **Exchange（交换机）**：接收生产者发送的消息，并根据路由键将消息路由到一个或多个队列
- **Binding（绑定）**：Exchange和Queue之间的链接
- **Routing Key（路由键）**：Exchange根据路由键将消息路由到Queue

### 1.2 Exchange类型

- **Direct Exchange**：根据路由键精确匹配队列
- **Fanout Exchange**：将消息广播到所有绑定的队列，忽略路由键
- **Topic Exchange**：根据路由键模式匹配队列
- **Headers Exchange**：根据消息头属性匹配队列

## 2. 项目中的RabbitMQ配置

项目使用Spring AMQP框架集成RabbitMQ，主要配置在`RabbitMQConfig`类中。

### 2.1 基础配置

```java
@Configuration
public class RabbitMQConfig {

    @Resource
    private ConnectionFactory connectionFactory;

    @Bean
    RabbitTemplate rabbitTemplate() {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return rabbitTemplate;
    }

    @Bean
    public RabbitListenerContainerFactory<?> rabbitListenerContainerFactory() {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(new Jackson2JsonMessageConverter());
        return factory;
    }
}
```

### 2.2 Exchange和Queue定义

项目中定义了多种类型的Exchange和Queue，用于不同的业务场景：

#### Direct Exchange示例

```java
@Bean
public DirectExchange alarmExchange() {
    return new DirectExchange(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM, true, false);
}

@Bean
public Queue modelDataAlarmQueue() {
    return new Queue(EolmConstant.Rabbit.QUEUE_SENSOR_MODEL_DATA_ALARM, true, false, false);
}

@Bean
public Binding modelDataAlarmBinding() {
    return BindingBuilder.bind(modelDataAlarmQueue()).to(alarmExchange()).with(EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM);
}
```

#### Fanout Exchange示例

```java
@Bean
public FanoutExchange messageFanoutExchange() {
    return new FanoutExchange(EolmConstant.Rabbit.FANOUT_EXCHANGE_MESSAGE, true, false);
}

@Bean
public Queue messageQueue() {
    return new Queue(EolmConstant.Rabbit.QUEUE_MESSAGE, true, false, false);
}

@Bean
public Binding messageBinding(){
    return BindingBuilder.bind(messageQueue()).to(messageFanoutExchange());
}
```

### 2.3 RoutingKey和Queue的绑定

RoutingKey和Queue的绑定是在Spring应用启动时，通过Binding对象实现的。在项目中，这种绑定主要通过以下方式完成：

#### Direct Exchange的绑定

对于Direct Exchange，需要指定RoutingKey进行绑定，这样Exchange可以根据消息的RoutingKey将消息路由到对应的Queue：

```java
@Bean
public Binding modelDataAlarmBinding() {
    return BindingBuilder.bind(modelDataAlarmQueue())  // 绑定队列
        .to(alarmExchange())                          // 到交换机
        .with(EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM);  // 使用指定的RoutingKey
}
```

在这个例子中：
- `modelDataAlarmQueue()` 是要绑定的队列
- `alarmExchange()` 是要绑定到的交换机
- `EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM` 是绑定使用的RoutingKey

当消息发送到 `alarmExchange` 交换机，并且指定的RoutingKey为 `EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM` 时，消息会被路由到 `modelDataAlarmQueue` 队列。

#### Fanout Exchange的绑定

对于Fanout Exchange，不需要指定RoutingKey，因为它会将消息广播到所有绑定的队列：

```java
@Bean
public Binding messageBinding(){
    return BindingBuilder.bind(messageQueue())  // 绑定队列
        .to(messageFanoutExchange());          // 到交换机，不需要指定RoutingKey
}
```

在这个例子中：
- `messageQueue()` 是要绑定的队列
- `messageFanoutExchange()` 是要绑定到的交换机

当消息发送到 `messageFanoutExchange` 交换机时，无论指定什么RoutingKey，消息都会被广播到所有绑定的队列，包括 `messageQueue`。

#### 绑定的时机

这些绑定是在Spring应用启动时，通过Spring的Bean初始化机制完成的：

1. Spring容器启动时，会初始化所有的Bean
2. 首先创建Exchange、Queue等Bean
3. 然后创建Binding Bean，此时会调用RabbitMQ的API将Queue绑定到Exchange，并指定RoutingKey（如果需要）

这样，当应用启动完成后，所有的Exchange、Queue和绑定关系都已经在RabbitMQ服务器中建立好了，可以开始发送和接收消息。

#### 消息路由逻辑

当生产者发送消息到Exchange时，Exchange会根据消息的RoutingKey和绑定关系，将消息路由到对应的Queue。不同类型的Exchange有不同的路由逻辑：

1. **Direct Exchange的路由逻辑**
   - 生产者发送消息到Direct Exchange，并指定RoutingKey
   - Exchange将消息路由到与该RoutingKey精确匹配的Queue
   - 如果没有与该RoutingKey绑定的Queue，消息将会被丢弃

   例如，当发送消息到`alarmExchange`并指定RoutingKey为`EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM`时：
   ```java
   rabbitTemplate.convertAndSend(
       EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
       EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM,
       sensorDataVO
   );
   ```
   消息将被路由到`modelDataAlarmQueue`队列，因为这个队列在配置中与该Exchange和RoutingKey绑定。

2. **Fanout Exchange的路由逻辑**
   - 生产者发送消息到Fanout Exchange，可以指定RoutingKey但会被忽略
   - Exchange将消息广播到所有与其绑定的Queue，不考虑RoutingKey

   例如，当发送消息到`messageFanoutExchange`时：
   ```java
   rabbitTemplate.convertAndSend(
       EolmConstant.Rabbit.FANOUT_EXCHANGE_MESSAGE,
       "", // 这里的RoutingKey会被忽略
       messageVo
   );
   ```
   消息将被广播到所有与`messageFanoutExchange`绑定的队列，包括`messageQueue`。

3. **Topic Exchange的路由逻辑**
   - 生产者发送消息到Topic Exchange，并指定RoutingKey
   - Exchange将消息路由到RoutingKey与绑定模式匹配的Queue
   - RoutingKey通常是由点分隔的字符串，如`user.create`
   - 绑定模式可以使用`*`（匹配一个单词）和`#`（匹配零个或多个单词）作为通配符

这种路由机制使得RabbitMQ能够灵活地处理不同类型的消息路由需求，是RabbitMQ强大功能的核心部分。

### 2.4 常量定义

项目中的RabbitMQ相关常量定义在`EolmConstant.Rabbit`接口中：

```java
interface Rabbit {
    // Arguments
    String MESSAGE_TTL = "x-message-ttl";

    // 通用指标 -> 报警门限新增队列
    String QUEUE_ALARM_THRESHOLD_ADD = "queue.alarm.threshold.add";
    // 通用指标 -> 报警门限新增路由
    String ROUTING_ALARM_THRESHOLD_ADD = "routing.alarm.threshold.add";

    // 消息中心交换机
    String FANOUT_EXCHANGE_MESSAGE = "fanout.exchange.message";
    // 消息中心队列
    String QUEUE_MESSAGE = "queue.message";

    // 更多常量定义...
}
```

## 3. 消息发送

### 3.1 使用RabbitTemplate发送消息

项目中使用`RabbitTemplate`发送消息，主要有以下几种方式：

#### 发送到Direct Exchange

```java
// 发送报警消息到Direct Exchange，指定路由键
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
    EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM,
    sensorDataVO
);
```

#### 发送到Fanout Exchange

```java
// 发送消息到Fanout Exchange，不需要指定路由键
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.FANOUT_EXCHANGE_MESSAGE,
    "",
    messageVo
);

// 或者使用null作为路由键
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.EXCHANGE_AI_WAVE,
    null,
    aiData
);
```

### 3.2 消息发送示例

#### 发送报警消息

```java
// 发送报警MQ消息给报警检测
log.debug("发送报警MQ消息给报警检测：sensorDataId = {}, monitorId = {}。",
    message.getSensorDataId(), message.getMonitorId());
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
    EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM,
    sensorDataVO
);
```

#### 发送钉钉消息

```java
// 发送钉钉消息
log.info(messageBizType.getMessage() + "-发送钉钉消息：==================={}", sensorCode);
Tenant tenant = SysCache.getTenant(tenantId);
DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(
    tenant.getTenantId(),
    tenant.getTenantName(),
    sensorCode,
    messageBizType.getCode(),
    JSONUtil.toJsonStr(sensorInstance)
);
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
    EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE,
    dingTalkMessage
);
```

#### 发送AI模型数据

```java
// 组装协议数据
AiDataVO aiData = new AiDataVO(equipmentId, wave.getId(), sensorCode, aiModelNames, originTime);
aiData.setEquipmentName(equipmentService.getById(equipmentId).getName());
if(Func.isNotEmpty(sensorDataList)){
    aiData.setFilePath(rootPath + sensorDataList.get(0).getWaveformUrl());
}
log.info("AI模型组装数据======================：{}", aiData);
// 将数据发送到MQ
rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_AI_WAVE, null, aiData);
```

## 4. 消息接收

### 4.1 使用@RabbitListener注解接收消息

项目中使用`@RabbitListener`注解来监听队列并接收消息：

```java
@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_MESSAGE)
public void pointValueReceive(MessageVo messageVo) {
    log.info("消息推送接收者收到消息：{}", messageVo);
    WebSocketServer.sendInfo(JSON.toJSONString(messageVo), messageVo.getTo());
}
```

### 4.2 使用RabbitMQHandler处理消息

项目中使用`RabbitMQHandler`类来处理从队列接收到的消息，并根据消息的命令类型分发给不同的业务处理器：

```java
@Component
@Slf4j
public class RabbitMQHandler {

    /**
     * 记录命令及相关业务信息
     */
    @Resource
    private Map<String, BusinessBean> commandBusiness;

    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 10,
        0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
        (r) -> new Thread(r, "[Thread]-Point-Value-Handler-" + threadPoolAtomic.getAndIncrement()));

    @RabbitListener(queues = EolmConstant.Rabbit.QUEUE_POINT_VALUE_EOLM)
    public void handler(MessageBean message) {
        //根据 command取相关业务处理Bean
        //调用Bean中的CommandExec
        //线程调用Bean中的业务处理
        BusinessBean businessBean = commandBusiness.get(message.getCommand());
        if (null == businessBean) {
            return;
        }

        boolean save = false;
        if (null != businessBean.getExec()) {
            save = businessBean.getExec().save(message);
        }

        if (!save || null == businessBean.getHandlers()) {
            return;
        }
        for (CommandBusiness commandBusiness : businessBean.getHandlers()) {
            threadPoolExecutor.execute(() -> commandBusiness.business(message));
        }
    }
}
```

## 5. 业务处理器

项目中定义了多种业务处理器，用于处理不同类型的消息：

### 5.1 CommandBusiness接口

```java
public interface CommandBusiness {
    /**
     * 获取命令
     * @return 命令
     */
    String getCommand();

    /**
     * 业务处理
     * @param message 消息
     */
    void business(MessageBean message);
}
```

### 5.2 业务处理器实现示例

#### 转速数据处理器

```java
@Component
@AllArgsConstructor
public class RpmSaveRedisBusiness extends AbstractCommandBusiness {

    private final SzykRedis szykRedis;

    @Override
    public String getCommand() {
        return Command.RPM_COMMAND;
    }

    @Override
    public void business(MessageBean message) {
        super.business(message);
        // 传感器数据最新数据
        if (SampledDataTypeEnum.RPM.getCode().equals(message.getType())) {
            // 转速数据
            if (message.getValue() != null) {
                // 转速最新值
                String redisKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
                    + StringPool.COLON + SampledDataTypeEnum.RPM.getCode();
                szykRedis.set(redisKey, message.getValue());

                // 测点最新转速
                String monitorRedisKey = message.getMonitorId() + StringPool.COLON + SampledDataTypeEnum.RPM.getCode();
                szykRedis.set(monitorRedisKey, message.getValue());
            }
        }
    }
}
```

#### 报警消息处理器

```java
@Component
@AllArgsConstructor
public class VibrateSendAlarmAndMechanismModelBusiness extends AbstractCommandBusiness {

    private final RabbitTemplate rabbitTemplate;

    @Override
    public String getCommand() {
        return Command.VIBRATE_COMMAND;
    }

    @Override
    public void business(MessageBean message) {
        super.business(message);

        SensorDataVO sensorDataVO = message.getSensorDataVO();
        boolean flag = sensorDataVO != null && !InvalidEnum.INVALID.getCode().equals(sensorDataVO.getInvalid())
            && new BigDecimal(message.getValue()).compareTo(message.getHaltLine()) > 0;
        if(!flag){
            log.warn("VibrateSendAlarmBusiness.business() - 异常的传感器数据，不发送报警！sensorDataId = {}, monitorId = {}",
                message.getSensorDataId(), message.getMonitorId());
            return;
        }
        // 停机数据过滤
        log.debug("发送报警MQ消息给报警检测：sensorDataId = {}, monitorId = {}。",
            message.getSensorDataId(), message.getMonitorId());
        rabbitTemplate.convertAndSend(
            EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
            EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM,
            sensorDataVO
        );
    }
}
```

## 6. 项目中RabbitMQ的应用场景

### 6.1 消息推送

使用RabbitMQ实现消息推送功能，将消息发送到消息中心，然后通过WebSocket推送给前端用户。

```java
// 发送消息
rabbitTemplate.convertAndSend(EolmConstant.Rabbit.FANOUT_EXCHANGE_MESSAGE, "", messageVo);

// 接收消息并推送给前端
@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_MESSAGE)
public void pointValueReceive(MessageVo messageVo) {
    log.info("消息推送接收者收到消息：{}", messageVo);
    WebSocketServer.sendInfo(JSON.toJSONString(messageVo), messageVo.getTo());
}
```

### 6.2 报警检测

使用RabbitMQ实现报警检测功能，将传感器数据发送到报警检测队列，然后由报警检测服务处理。

```java
// 发送报警消息
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
    EolmConstant.Rabbit.ROUTING_MODEL_DATA_ALARM,
    sensorDataVO
);
```

### 6.3 AI模型分析

使用RabbitMQ实现AI模型分析功能，将波形数据发送到AI模型分析队列，然后由AI模型分析服务处理。

```java
// 发送AI模型数据
rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_AI_WAVE, null, aiData);
```

### 6.4 钉钉消息通知

使用RabbitMQ实现钉钉消息通知功能，将消息发送到钉钉消息队列，然后由钉钉消息服务处理。

```java
// 发送钉钉消息
rabbitTemplate.convertAndSend(
    EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
    EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE,
    dingTalkMessage
);
```

## 7. 最佳实践

### 7.1 消息发送

- 使用`convertAndSend`方法发送消息，自动将对象转换为JSON格式
- 根据业务场景选择合适的Exchange类型
- 使用常量定义Exchange名称、Queue名称和Routing Key

### 7.2 消息接收

- 使用`@RabbitListener`注解监听队列
- 使用业务处理器模式处理不同类型的消息
- 使用线程池异步处理消息，避免阻塞消息接收线程

### 7.3 异常处理

- 在消息处理过程中捕获异常，避免消息丢失
- 对于处理失败的消息，可以记录到日志或者重新发送

## 8. 总结

RabbitMQ在项目中扮演着重要的角色，主要用于以下场景：

1. 消息推送：将消息发送到消息中心，然后推送给前端用户
2. 报警检测：将传感器数据发送到报警检测队列，实现报警功能
3. AI模型分析：将波形数据发送到AI模型分析队列，实现AI分析功能
4. 钉钉消息通知：将消息发送到钉钉消息队列，实现钉钉通知功能

通过RabbitMQ，项目实现了不同服务之间的解耦，提高了系统的可扩展性和可维护性。
