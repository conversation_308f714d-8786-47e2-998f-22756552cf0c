# 异常生成完整逻辑梳理

## 1. 概述

本文档详细梳理了AI运维平台中异常生成的完整逻辑流程，包括数据接收、报警处理、二次确认、权重计算和异常生成等各个环节。

## 2. 整体架构

```mermaid
graph TD
    A[传感器数据] --> B[MQ消息队列]
    B --> C[AlarmListener监听器]
    C --> D[AlarmLogicService报警逻辑]
    D --> E[门限/机理/AI报警处理]
    E --> F[Redis缓存标志位]
    F --> G[二次确认触发]
    G --> H[AbnormalLogicService异常逻辑]
    H --> I[权重计算]
    I --> J[异常生成判断]
    J --> K[异常记录保存]
```

## 3. 数据流入与监听

### 3.1 AlarmListener监听器

系统通过RabbitMQ监听三种类型的报警数据：

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/listener/AlarmListener.java" mode="EXCERPT">
````java
@RabbitHandler
@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_SENSOR_MODEL_DATA_ALARM)
public void handleData(SensorDataVO sensorData) {
    log.info("=====》报警模块开始处理传感器采样数据：========{}", sensorData);
    alarmLogicService.handleSensorData(sensorData);
    log.info("=====》报警模块传感器采样数据处理结束：========{}", sensorData);
}

@RabbitHandler
@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_AI_ALARM)
public void handleData(AiAlarmResultVO data, org.springframework.amqp.core.Message message,
                com.rabbitmq.client.Channel channel) throws java.io.IOException {
    // AI报警数据处理
}

@RabbitHandler
@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_MECHANISM_MODEL_ALARM)
public void handleData(ModelAlarmResultVO data) {
    // 机理模型报警数据处理
}
````
</augment_code_snippet>

### 3.2 三种报警类型

1. **门限报警（Threshold）**：传感器数据超过预设阈值
2. **AI报警（Intelligence）**：AI模型诊断出异常
3. **机理报警（Mechanism）**：机理模型诊断出故障

## 4. 报警处理逻辑

### 4.1 门限报警处理

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public void handleSensorData(SensorDataVO sensorData) {
    // 获取监测点信息
    MonitorDTO monitor = monitorService.getByIdIncludeEquipment(sensorData.getMonitorId());
    AlarmDetailVO alarmDetailVO = new AlarmDetailVO().toAlarmDetailVO(monitor, AlarmBizTypeEnum.THRESHOLD, null, sensorData.getOriginTime());

    // 门限比较和报警生成
    if (alarmLevel != null) {
        // 门限报警生成异常
        if (AlarmIndexEnum.EFFECTIVE_VALUE == AlarmIndexEnum.getByCode(alarmRecord.getAlarmIndex())) {
            this.setRedisData(KEY_ALARM_HANDLER[0], alarmRecord.getWaveId(), alarmRecord.getAlarmTime(), 2);
            this.sensorDataHandler(alarmDetailVO.getTenantId(), alarmRecord.getWaveId() + StringPool.COLON +
                alarmRecord.getAlarmTime().getTime() + StringPool.COLON + KEY_ALARM_HANDLER[0]);
        }
    }
}
````
</augment_code_snippet>

### 4.2 AI报警处理

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public void handleAiAlarmData(AiAlarmResultVO vo) {
    // AI诊断正常
    if (Func.equals(Func.toInt(StringPool.ZERO), vo.getResultCode())) {
        this.setRedisData(KEY_ALARM_HANDLER[2], vo.getWaveId(), vo.getOriginTime(), 1);
        return;
    }

    // AI报警生成异常
    this.setRedisData(KEY_ALARM_HANDLER[2], alarmRecord.getWaveId(), alarmRecord.getAlarmTime(), 2);
    this.sensorDataHandler(equipment.getTenantId(), alarmRecord.getWaveId() + StringPool.COLON +
        alarmRecord.getAlarmTime().getTime() + StringPool.COLON + KEY_ALARM_HANDLER[2]);
}
````
</augment_code_snippet>

### 4.3 机理报警处理

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public void handleModelAlarmData(ModelAlarmResultVO vo) {
    // 机理报警生成异常
    this.sensorDataHandler(monitor.getTenantId(), vo.getWaveId() + StringPool.COLON +
        originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[1]);
}
````
</augment_code_snippet>

## 5. Redis缓存标志位机制

### 5.1 标志位设置

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public synchronized void setRedisData(String type, Long waveId, Date originTime, Integer value) {
    log.info("设置REDIS标志位：=====================波形：{}，类型：{}，时间：{}", waveId, type, originTime.getTime());
    String key = waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + type;
    szykRedis.setEx(key, value, Duration.ofMinutes(5));
}
````
</augment_code_snippet>

### 5.2 标志位含义

- **KEY_ALARM_HANDLER[0]** = "threshold"：门限报警
- **KEY_ALARM_HANDLER[1]** = "mechanism"：机理报警
- **KEY_ALARM_HANDLER[2]** = "ai"：AI报警

标志位值：
- **1**：正常状态
- **2**：报警状态

## 6. 二次确认机制

### 6.1 触发条件

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public synchronized void sensorDataHandler(String tenantId, String key) {
    // 以机理模型为主判断是否生成异常
    if (key.contains(KEY_ALARM_HANDLER[1])) {
        if (Func.isNotEmpty((Object) szykRedis.get(key))) {
            // 二次确认逻辑
            abnormalLogicService.alarmDataHandler(tenantId, waveId, new Date(Func.toLong(keyArr[1])));
        }
    }
}
````
</augment_code_snippet>

### 6.2 二次确认逻辑

系统以**机理报警为主导**，当机理报警触发时，会检查其他类型报警的状态，进行综合判断。

## 7. 异常生成策略

### 7.1 策略配置

系统支持两种异常生成策略：

1. **连续策略（ContinuousStrategy）**：要求连续多次报警才生成异常
2. **非连续策略（DiscontinuousStrategy）**：在一定时间内累计报警次数达到阈值

### 7.2 策略参数

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/vo/AbnormalParamVO.java" mode="EXCERPT">
````java
@Data
@ApiModel(value = "AbnormalParamVO", description = "AbnormalParamVO")
public class AbnormalParamVO implements Serializable {
    // 最大权重
    @ApiModelProperty(value = "最大权重")
    private Integer maxWeight;
    // 连续策略-超限累加权重
    @ApiModelProperty(value = "连续策略-超限累加权重")
    private Integer accumulatedWeight;
    // 连续策略-连续超限次数
    @ApiModelProperty(value = "连续策略-连续超限次数")
    private Integer continuousTimes;
    // 非连续策略-统计天数
    @ApiModelProperty(value = "非连续策略-统计天数")
    private Integer statisticsDays;
    // 非连续策略-报警次数
    @ApiModelProperty(value = "非连续策略-报警次数")
    private Integer alarmTimes;
    // 是否启用
    @ApiModelProperty(value = "是否启用（0：停用，1：启用）")
    private Integer isEnabled;
}
````
</augment_code_snippet>

## 8. 权重计算逻辑

### 8.1 连续策略权重计算

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
// 连续6次报警，则异常
if (Func.equals(continuousTimes, alarmRecordList.size())) {
    Integer weight = accumulatedWeight * continuousTimes > maxWeight ? maxWeight : accumulatedWeight * continuousTimes;
    abnormalRecord.setAbnormalLevel(alarmLevel).setWeight(weight);
    return abnormalRecord;
}
````
</augment_code_snippet>

### 8.2 非连续策略权重计算

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
// 3天内，报警次数大于等于80次
if (alarmRecordList.size() >= alarmTimes) {
    abnormalRecord.setAbnormalLevel(alarmLevel).setWeight(maxWeight);
    return abnormalRecord;
}
````
</augment_code_snippet>

## 9. 异常生成判断

### 9.1 权重汇总判断

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
Integer weightSum = thresholdWeight + mechanismWeight + aiWeight;
// 生成异常明细
if (weightSum >= discontinuousStrategy.getWeightSum()) {
    if (thresholdWeight > 0) {
        detailList.add(thresholdDetail);
    }
    if (mechanismWeight > 0) {
        detailList.add(mechanismDetail);
    }
    if (aiWeight > 0) {
        detailList.add(aiDetail);
    }
}
````
</augment_code_snippet>

### 9.2 异常等级确定

系统根据报警记录的等级，取中位数作为异常等级：

- **1级**：轻度异常
- **2级**：一般异常
- **3级**：重度异常
- **4级**：严重异常

## 10. 关键常量定义

```java
private static final String[] KEY_ALARM_HANDLER = {"threshold", "mechanism", "ai"};
private static final Integer ALARM_FLAG = 2; // 报警标志
```

## 11. 详细流程图

### 11.1 异常生成主流程

```mermaid
flowchart TD
    A[传感器数据/AI诊断/机理诊断] --> B{数据类型判断}
    B -->|门限数据| C[handleSensorData]
    B -->|AI诊断| D[handleAiAlarmData]
    B -->|机理诊断| E[handleModelAlarmData]

    C --> F[门限比较]
    F -->|超限| G[生成门限报警记录]
    F -->|正常| H[设置Redis标志位=1]
    G --> I[设置Redis标志位=2]

    D --> J{AI诊断结果}
    J -->|正常| K[设置Redis标志位=1]
    J -->|异常| L[生成AI报警记录]
    L --> M[设置Redis标志位=2]

    E --> N[生成机理报警记录]
    N --> O[设置Redis标志位=2]

    I --> P[sensorDataHandler]
    M --> P
    O --> P

    P --> Q{是否机理报警触发?}
    Q -->|否| R[结束]
    Q -->|是| S[abnormalLogicService.alarmDataHandler]

    S --> T[获取异常生成策略配置]
    T --> U{策略类型}
    U -->|连续策略| V[连续策略权重计算]
    U -->|非连续策略| W[非连续策略权重计算]

    V --> X[权重汇总]
    W --> X
    X --> Y{权重和>=阈值?}
    Y -->|否| Z[不生成异常]
    Y -->|是| AA[生成异常记录]

    AA --> BB[更新设备异常状态]
    BB --> CC[发送异常消息通知]
```

### 11.2 权重计算详细流程

```mermaid
flowchart TD
    A[开始权重计算] --> B{策略类型}

    B -->|连续策略| C[获取连续策略配置]
    B -->|非连续策略| D[获取非连续策略配置]

    C --> E[查询连续时间段内报警记录]
    E --> F{连续报警次数=配置次数?}
    F -->|是| G[权重=累加权重×次数]
    F -->|否| H[权重=0]
    G --> I{权重>最大权重?}
    I -->|是| J[权重=最大权重]
    I -->|否| K[保持计算权重]

    D --> L[查询统计天数内报警记录]
    L --> M{报警次数>=配置次数?}
    M -->|是| N[权重=最大权重]
    M -->|否| O[权重=0]

    H --> P[返回权重结果]
    J --> P
    K --> P
    N --> P
    O --> P
```

## 12. 关键代码分析

### 12.1 二次确认核心逻辑

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
public void alarmDataHandler(String tenantId, Long waveId, Date originTime) {
    // 检查是否已存在故障状态的异常
    Abnormal abnormal = abnormalService.getOne(Wrappers.<Abnormal>query().lambda()
        .eq(Abnormal::getEquipmentId, waveDTO.getEquipmentId())
        .eq(Abnormal::getStatus, AbnormalStatusEnum.IS_FAULT.getCode()));
    if (Func.isNotEmpty(abnormal)) {
        return; // 已存在故障，不再生成新异常
    }

    // 获取Redis中的报警标志位
    String thresholdKey = waveId + ":" + originTime.getTime() + ":threshold";
    String mechanismKey = waveId + ":" + originTime.getTime() + ":mechanism";
    String aiKey = waveId + ":" + originTime.getTime() + ":ai";

    Object thresholdAlarm = szykRedis.get(thresholdKey);
    Object mechanismAlarm = szykRedis.get(mechanismKey);
    Object aiAlarm = szykRedis.get(aiKey);

    // 分别计算连续策略和非连续策略的权重
    // 最终根据权重和判断是否生成异常
}
````
</augment_code_snippet>

### 12.2 异常自动关闭逻辑

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
public void autoCloseAbnormal(String tenantId, Long waveId, Date originTime) {
    // 重新计算权重
    // 如果权重和小于阈值，则自动关闭异常
    if ((thresholdWeight + mechanismWeight + aiWeight) < continuousStrategy.getWeightSum()) {
        abnormalRecordService.update(Wrappers.<AbnormalRecord>update().lambda()
            .set(AbnormalRecord::getStatus, AlarmStatusEnum.CLOSED.getCode())
            .set(AbnormalRecord::getCloseTime, DateUtil.now()));
    }
}
````
</augment_code_snippet>

## 13. 配置参数说明

### 13.1 连续策略参数

| 参数名 | 说明 | 示例值 |
|--------|------|--------|
| continuousTimes | 连续超限次数 | 6 |
| accumulatedWeight | 超限累加权重 | 10 |
| maxWeight | 最大权重 | 100 |
| weightSum | 权重和阈值 | 100 |

### 13.2 非连续策略参数

| 参数名 | 说明 | 示例值 |
|--------|------|--------|
| statisticsDays | 统计天数 | 3 |
| alarmTimes | 报警次数阈值 | 80 |
| maxWeight | 最大权重 | 100 |
| weightSum | 权重和阈值 | 100 |

## 14. 异常状态流转

```mermaid
stateDiagram-v2
    [*] --> 待处理: 异常生成
    待处理 --> 已成故障: 人工确认为故障
    待处理 --> 已关闭: 人工关闭/自动关闭
    已成故障 --> 已关闭: 故障处理完成
    已关闭 --> [*]
```

## 15. 总结

异常生成的完整流程：

1. **数据接收**：通过MQ监听三种类型的报警数据
2. **报警处理**：根据不同类型进行相应的报警逻辑处理
3. **标志位设置**：在Redis中设置报警状态标志位
4. **二次确认**：以机理报警为主导，触发异常生成逻辑
5. **策略计算**：根据连续/非连续策略计算权重
6. **异常判断**：权重达到阈值时生成异常记录
7. **状态更新**：更新设备和测点的异常状态

### 15.1 关键特点

- **多源融合**：综合门限、AI、机理三种报警源
- **二次确认**：避免误报，提高准确性
- **权重机制**：灵活的权重计算和阈值判断
- **自动关闭**：当条件不满足时自动关闭异常
- **状态管理**：完整的异常状态流转机制

### 15.2 优势

- **准确性高**：多重验证机制减少误报
- **灵活配置**：支持不同的策略配置
- **实时性强**：基于MQ的实时数据处理
- **可追溯**：完整的异常生成和处理记录

整个流程确保了异常生成的准确性和可靠性，为设备运维提供了可靠的异常检测能力。
