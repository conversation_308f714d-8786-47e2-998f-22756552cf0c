# 异常详情与异常记录关系详解

## 1. 核心结论

✅ **一个异常详情（AbnormalDetail）可以包含多个异常记录（AbnormalRecord）**

✅ **一个异常详情可以同时包含AI、机理、门限三种类型的异常**

## 2. 数据结构关系

### 2.1 层级关系图

```mermaid
graph TD
    A[设备异常 Abnormal] --> B[异常详情1 AbnormalDetail]
    A --> C[异常详情2 AbnormalDetail]
    A --> D[异常详情N AbnormalDetail]
    
    B --> E[异常记录1 AbnormalRecord - 门限]
    B --> F[异常记录2 AbnormalRecord - 机理]
    B --> G[异常记录3 AbnormalRecord - AI]
    
    C --> H[异常记录4 AbnormalRecord - 门限]
    C --> I[异常记录5 AbnormalRecord - 机理]
    
    D --> J[异常记录N AbnormalRecord]
```

### 2.2 实体关系

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/ops/entity/AbnormalDetail.java" mode="EXCERPT">
````java
@TableName("eolm_abnormal_detail")
public class AbnormalDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 异常ID - 关联到Abnormal
     */
    private Long abnormalId;
    /**
     * 波形ID - 一个异常详情对应一个波形
     */
    private Long waveId;
    /**
     * 异常等级
     */
    private Integer abnormalLevel;
    /**
     * 异常时间
     */
    private Date abnormalTime;
}
````
</augment_code_snippet>

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/ops/entity/AbnormalRecord.java" mode="EXCERPT">
````java
@TableName("eolm_abnormal_record")
public class AbnormalRecord implements Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * 异常ID - 关联到Abnormal
     */
    private Long abnormalId;
    /**
     * 波形ID - 关联到具体波形
     */
    private Long waveId;
    /**
     * 异常类型 - 1:门限 2:机理 3:AI
     */
    private Integer abnormalType;
    /**
     * 机理类型 - 具体的机理模型类型
     */
    private Integer mechanismType;
    /**
     * 权重
     */
    private Integer weight;
}
````
</augment_code_snippet>

## 3. 异常生成逻辑

### 3.1 权重计算与异常生成

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
public void alarmDataHandler(String tenantId, Long waveId, Date originTime) {
    // 1. 分别计算三种类型的权重
    AbnormalRecordVO thresholdRecord = this.continuousThresholdWeight(waveId, originTime);
    List<AbnormalRecordVO> mechanismRecordList = this.continuousMechanismWeight(waveId, originTime);
    AbnormalRecordVO aiRecord = this.continuousAiWeight(waveId, originTime);
    
    // 2. 计算权重总和
    Integer thresholdWeight = Func.isNotEmpty(thresholdRecord) ? thresholdRecord.getWeight() : 0;
    Integer mechanismWeight = mechanismRecordList.stream().mapToInt(AbnormalRecordVO::getWeight).sum();
    Integer aiWeight = Func.isNotEmpty(aiRecord) ? aiRecord.getWeight() : 0;
    Integer weightSum = thresholdWeight + mechanismWeight + aiWeight;
    
    // 3. 如果权重达到阈值，生成异常详情
    if (weightSum >= strategy.getWeightSum()) {
        // 创建一个异常详情，包含多个异常记录
        AbnormalDetailVO abnormalDetailVO = new AbnormalDetailVO();
        abnormalDetailVO.setWaveId(waveId).setAbnormalTime(originTime);
        
        List<AbnormalRecordVO> recordList = new ArrayList<>();
        if (thresholdWeight > 0) {
            recordList.add(thresholdRecord);  // 添加门限异常记录
        }
        if (mechanismWeight > 0) {
            recordList.addAll(mechanismRecordList);  // 添加机理异常记录
        }
        if (aiWeight > 0) {
            recordList.add(aiRecord);  // 添加AI异常记录
        }
        
        // 一个异常详情包含多个异常记录
        abnormalDetailVO.setAbnormalRecordList(recordList);
    }
}
````
</augment_code_snippet>

### 3.2 异常详情VO结构

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/ops/vo/AbnormalDetailVO.java" mode="EXCERPT">
````java
public class AbnormalDetailVO extends AbnormalDetail {
    /**
     * 异常类型（字典：alarm_biz_type）
     */
    private Integer abnormalType;
    
    /**
     * 异常明细列表 - 关键字段！
     * 一个异常详情可以包含多个异常记录
     */
    @ApiModelProperty(value = "异常明细列表")
    private List<AbnormalRecordVO> abnormalRecordList;
}
````
</augment_code_snippet>

## 4. 实际应用场景

### 4.1 场景1：单一类型异常

```java
// 只有门限报警
WaveId: 1001 (X方向振动)
├── AbnormalDetail (异常详情)
    └── AbnormalRecord (门限异常记录)
        ├── abnormalType: 1 (门限)
        ├── weight: 60
        └── abnormalLevel: 2
```

### 4.2 场景2：复合类型异常

```java
// 门限 + 机理 + AI 三种异常同时发生
WaveId: 1001 (X方向振动)
├── AbnormalDetail (异常详情)
    ├── AbnormalRecord1 (门限异常记录)
    │   ├── abnormalType: 1 (门限)
    │   ├── weight: 30
    │   └── abnormalLevel: 2
    ├── AbnormalRecord2 (机理异常记录)
    │   ├── abnormalType: 2 (机理)
    │   ├── mechanismType: 5 (不平衡)
    │   ├── weight: 50
    │   └── abnormalLevel: 3
    └── AbnormalRecord3 (AI异常记录)
        ├── abnormalType: 3 (AI)
        ├── weight: 40
        └── abnormalLevel: 2
```

### 4.3 场景3：多机理类型异常

```java
// 同时检测到多种机理异常
WaveId: 1001 (X方向振动)
├── AbnormalDetail (异常详情)
    ├── AbnormalRecord1 (机理异常记录 - 不平衡)
    │   ├── abnormalType: 2 (机理)
    │   ├── mechanismType: 5 (不平衡)
    │   └── weight: 40
    ├── AbnormalRecord2 (机理异常记录 - 松动)
    │   ├── abnormalType: 2 (机理)
    │   ├── mechanismType: 8 (松动)
    │   └── weight: 30
    └── AbnormalRecord3 (门限异常记录)
        ├── abnormalType: 1 (门限)
        └── weight: 35
```

## 5. 数据库存储结构

### 5.1 表关系

```mermaid
erDiagram
    ABNORMAL ||--o{ ABNORMAL_DETAIL : "一对多"
    ABNORMAL_DETAIL ||--o{ ABNORMAL_RECORD : "一对多"
    
    ABNORMAL {
        Long id "异常ID"
        Long equipmentId "设备ID"
        Integer abnormalLevel "异常等级"
        String abnormalReason "异常原因"
    }
    
    ABNORMAL_DETAIL {
        Long id "异常详情ID"
        Long abnormalId "异常ID"
        Long waveId "波形ID"
        Integer abnormalLevel "异常等级"
        Date abnormalTime "异常时间"
    }
    
    ABNORMAL_RECORD {
        Long id "异常记录ID"
        Long abnormalId "异常ID"
        Long waveId "波形ID"
        Integer abnormalType "异常类型"
        Integer mechanismType "机理类型"
        Integer weight "权重"
    }
```

### 5.2 数据示例

**异常表（eolm_abnormal）**
```sql
id: 1001
equipmentId: 2001
abnormalLevel: 3
abnormalReason: "1,2,3"  -- 包含门限、机理、AI三种类型
```

**异常详情表（eolm_abnormal_detail）**
```sql
id: 3001
abnormalId: 1001
waveId: 4001
abnormalLevel: 3
abnormalTime: "2024-01-01 10:05:00"
```

**异常记录表（eolm_abnormal_record）**
```sql
-- 门限异常记录
id: 5001, abnormalId: 1001, waveId: 4001, abnormalType: 1, weight: 30

-- 机理异常记录1
id: 5002, abnormalId: 1001, waveId: 4001, abnormalType: 2, mechanismType: 5, weight: 50

-- 机理异常记录2
id: 5003, abnormalId: 1001, waveId: 4001, abnormalType: 2, mechanismType: 8, weight: 30

-- AI异常记录
id: 5004, abnormalId: 1001, waveId: 4001, abnormalType: 3, weight: 40
```

## 6. 异常详情保存逻辑

### 6.1 addAbnormalDetail方法

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
private boolean addAbnormalDetail(AbnormalVO abnormalVO, List<AbnormalDetailVO> list) {
    // 收集所有异常记录
    List<AbnormalRecordVO> records = new ArrayList<>();
    String abnormalReason = "";
    
    // 按异常类型分组处理
    Map<Integer, List<AbnormalDetailVO>> detailMap = list.stream()
        .collect(Collectors.groupingBy(AbnormalDetailVO::getAbnormalType));
    
    for (Integer key : detailMap.keySet()) {
        List<AbnormalDetailVO> details = detailMap.get(key);
        for (AbnormalDetailVO vo : details) {
            // 将每个异常详情的所有异常记录收集起来
            records.addAll(vo.getAbnormalRecordList());
        }
        abnormalReason += key + StringPool.COMMA;
    }
    
    // 查询或创建异常详情
    AbnormalDetail abnormalDetail = abnormalDetailService.getOne(
        Wrappers.<AbnormalDetail>query().lambda()
            .eq(AbnormalDetail::getAbnormalId, abnormalVO.getId())
            .eq(AbnormalDetail::getWaveId, waveDTO.getId())
            .eq(AbnormalDetail::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));
    
    // 保存异常记录
    for (AbnormalRecordVO record : records) {
        record.setAbnormalId(abnormalVO.getId());
        abnormalRecordService.saveOrUpdate(AbnormalRecordWrapper.build().voEntity(record));
    }
}
````
</augment_code_snippet>

## 7. 查询和展示逻辑

### 7.1 异常详情查询

在异常详情查询时，系统会：

1. **查询异常详情**：根据abnormalId和waveId查询异常详情
2. **查询异常记录**：根据abnormalId和waveId查询所有相关的异常记录
3. **按类型分组**：将异常记录按abnormalType分组
4. **组装数据**：将分组后的异常记录组装到异常详情中

### 7.2 前端展示效果

```
设备异常详情
├── 监测点：主轴承
    └── 波形：X方向振动
        ├── 门限报警：超限30%，权重30
        ├── 机理诊断：不平衡故障，权重50
        ├── 机理诊断：松动故障，权重30
        └── AI诊断：异常模式，权重40
        总权重：150，异常等级：3级
```

## 8. 关键特点总结

### 8.1 设计优势

1. **灵活性**：一个异常详情可以包含多种类型的异常
2. **可追溯性**：每个异常记录都有明确的类型和权重
3. **扩展性**：可以轻松添加新的异常类型
4. **聚合性**：多个异常记录聚合为一个异常详情

### 8.2 数据完整性

1. **权重累加**：多个异常记录的权重会累加
2. **等级继承**：异常详情的等级取最高的异常记录等级
3. **时间一致**：所有相关记录使用相同的异常时间
4. **状态同步**：异常详情和异常记录的状态保持同步

### 8.3 业务价值

1. **综合诊断**：提供多维度的异常诊断信息
2. **精确定位**：能够精确定位异常的具体原因
3. **决策支持**：为运维人员提供全面的决策信息
4. **趋势分析**：支持异常趋势和模式分析

## 9. 总结

**核心结论**：
- ✅ 一个异常详情可以包含多个异常记录
- ✅ 一个异常详情可以同时包含AI、机理、门限三种类型的异常
- ✅ 异常记录按类型分组，每种类型可以有多个记录（如多种机理类型）
- ✅ 权重累加，等级取最高值，提供综合的异常评估

这种设计确保了异常检测的全面性和准确性，为设备运维提供了丰富的诊断信息。
