# 硬件传感器与系统关联机制详解

## 1. 核心结论

✅ **硬件传感器与系统的关联主要通过传感器编码（sensorCode）实现**

✅ **波形ID（waveId）是系统内部标识符，不直接用于硬件关联**

✅ **数据传输时使用传感器编码，系统内部转换为波形ID进行处理**

## 2. 关联机制概览

```mermaid
flowchart TD
    A[硬件传感器] --> B[传感器编码 sensorCode]
    B --> C[MQ消息传输]
    C --> D[系统接收数据]
    D --> E[根据sensorCode查找Wave]
    E --> F[获取waveId]
    F --> G[数据存储和处理]
    
    A1[物理传感器: TEMP_001] --> B1[编码: TEMP_001]
    B1 --> C1[MessageBean.id = "TEMP_001"]
    C1 --> D1[AlarmListener接收]
    D1 --> E1[查询Wave表]
    E1 --> F1[waveId: 123456]
    F1 --> G1[存储到InfluxDB]
```

## 3. 数据传输标识字段

### 3.1 MessageBean结构

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/rabbit/handler/MessageBean.java" mode="EXCERPT">
````java
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class MessageBean implements Command {
    
    /**
     * 操作命令 - 数据类型标识
     */
    private String command;

    /**
     * 传感器ID - 传感器唯一标识码（传感器编码）
     * 这是硬件传感器与系统关联的关键字段
     */
    private String id;  // 对应sensorCode

    /**
     * 采集站id（网关id）
     */
    private String stationId;

    /**
     * 轴向，0：Z轴，1：X轴，2：Y轴
     */
    private Integer axis;

    /**
     * 采样数据类型
     */
    private String type;

    /**
     * 值（如温度值、电量值、压力值等）
     */
    private String value;

    /**
     * 采样时间
     */
    private Long originTime;

    /**
     * 波形数据
     */
    private String wave;
}
````
</augment_code_snippet>

### 3.2 关键关联字段

| 字段名 | 作用 | 示例值 | 说明 |
|--------|------|--------|------|
| **id** | 传感器唯一标识码 | "TEMP_001" | 硬件传感器的编码，关联的核心字段 |
| command | 数据类型命令 | "acceleration" | 标识数据类型（加速度、温度等） |
| axis | 轴向方向 | 1 | X轴=1, Y轴=2, Z轴=0 |
| type | 采样数据类型 | "acceleration" | 与command类似，数据类型标识 |

## 4. 系统内部关联流程

### 4.1 数据接收和转换

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/rabbit/exec/AbstractSensorDataExecutor.java" mode="EXCERPT">
````java
@Override
public synchronized boolean save(MessageBean message) {
    // 1. 根据传感器编码查找对应的Wave
    Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
        .eq(Wave::getSensorCode, message.getId())  // 关键：通过sensorCode查找
        .eq(Wave::getSampleDataType, message.getCommand())
        .eq(Wave::getMeasureDirection, getMeasureDirection(message.getAxis()))
        .eq(Wave::getUnbind, 0));

    if (wave == null) {
        log.error("未找到对应的波形配置，sensorCode={}, command={}, axis={}", 
                  message.getId(), message.getCommand(), message.getAxis());
        return false;
    }

    // 2. 创建传感器数据，使用Wave的ID
    SensorData sensorData = new SensorData()
        .setWaveId(wave.getId())  // 系统内部使用waveId
        .setSensorCode(message.getId())  // 保留原始传感器编码
        .setMonitorId(wave.getMonitorId())
        .setOriginTime(new Date(message.getOriginTime()));

    // 3. 存储到InfluxDB，使用waveId作为标识
    influxdbTools.insert(wave.getMonitorId().toString(), 
                        wave.getId().toString(),  // 使用waveId
                        data, 
                        sensorData.getOriginTime().getTime());
}
````
</augment_code_snippet>

### 4.2 Wave表的关联字段

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/Wave.java" mode="EXCERPT">
````java
@TableName("sidas_wave")
public class Wave implements Serializable {
    /**
     * 主键 - 波形ID（系统内部标识）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 传感器实例编码（硬件关联字段）
     */
    @ApiModelProperty(value = "传感器实例编码")
    private String sensorCode;
    
    /**
     * 采样数据类型（数据类型区分）
     */
    @ApiModelProperty(value = "采样数据类型")
    private String sampleDataType;
    
    /**
     * 测量方向（轴向区分）
     */
    @ApiModelProperty(value = "测量方向")
    private Integer measureDirection;
    
    /**
     * 编号，仅应力波传感器
     */
    @ApiModelProperty(value = "编号，仅应力波传感器")
    private String number;
    
    /**
     * 相位（A、B、C），仅电流传感器
     */
    @ApiModelProperty(value = "相位（A、B、C），仅电流传感器")
    private String phase;
}
````
</augment_code_snippet>

## 5. 关联查询逻辑

### 5.1 多维度匹配

系统通过多个维度来精确匹配硬件传感器与Wave：

```java
// 查找Wave的完整逻辑
Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
    .eq(Wave::getSensorCode, message.getId())           // 传感器编码匹配
    .eq(Wave::getSampleDataType, message.getCommand())  // 数据类型匹配
    .eq(Wave::getMeasureDirection, measureDirection)    // 测量方向匹配
    .eq(Wave::getNumber, message.getNumber())           // 编号匹配（应力波）
    .eq(Wave::getPhase, message.getPhase())             // 相位匹配（电流）
    .eq(Wave::getUnbind, 0));                          // 未解绑状态
```

### 5.2 测量方向转换

<augment_code_snippet path="szyk-zbusiness/zbusiness-basic/src/main/java/com/snszyk/zbusiness/basic/service/impl/SensorInstanceServiceImpl.java" mode="EXCERPT">
````java
/**
 * 获取测量方向
 * @param axisCount 轴数
 * @param installDirection 安装方向
 * @param axialDirection 轴向方向
 * @return 测量方向
 */
private Integer getMeasureDirection(Integer axisCount, Integer installDirection, Integer axialDirection) {
    // 根据硬件的轴向信息转换为系统的测量方向
    // 0：Z轴，1：X轴，2：Y轴
    if (axialDirection == null) {
        return 0;
    }
    
    // 三轴传感器的方向映射
    if (axisCount == 3) {
        switch (axialDirection) {
            case 0: return 3; // Z轴
            case 1: return 1; // X轴  
            case 2: return 2; // Y轴
            default: return 0;
        }
    }
    
    return axialDirection;
}
````
</augment_code_snippet>

## 6. 实际应用示例

### 6.1 温振一体传感器

```java
// 硬件传感器：TEMP_001（三轴温振一体传感器）
// 发送多种类型的数据

// X轴加速度数据
MessageBean message1 = new MessageBean()
    .setId("TEMP_001")           // 传感器编码
    .setCommand("acceleration")   // 数据类型
    .setAxis(1)                  // X轴
    .setValue("2.5")
    .setOriginTime(System.currentTimeMillis());

// Y轴加速度数据  
MessageBean message2 = new MessageBean()
    .setId("TEMP_001")           // 同一传感器编码
    .setCommand("acceleration")   // 同一数据类型
    .setAxis(2)                  // Y轴（不同轴向）
    .setValue("3.1")
    .setOriginTime(System.currentTimeMillis());

// 温度数据
MessageBean message3 = new MessageBean()
    .setId("TEMP_001")           // 同一传感器编码
    .setCommand("temperature")    // 不同数据类型
    .setAxis(null)               // 温度无轴向
    .setValue("45.2")
    .setOriginTime(System.currentTimeMillis());
```

### 6.2 系统内部Wave映射

```sql
-- Wave表中对应的记录
-- X轴加速度Wave
INSERT INTO sidas_wave (id, sensor_code, sample_data_type, measure_direction, wave_name) 
VALUES (1001, 'TEMP_001', 'acceleration', 1, 'X方向加速度');

-- Y轴加速度Wave  
INSERT INTO sidas_wave (id, sensor_code, sample_data_type, measure_direction, wave_name)
VALUES (1002, 'TEMP_001', 'acceleration', 2, 'Y方向加速度');

-- 温度Wave
INSERT INTO sidas_wave (id, sensor_code, sample_data_type, measure_direction, wave_name)
VALUES (1003, 'TEMP_001', 'temperature', 0, '温度');
```

### 6.3 数据流转过程

```mermaid
sequenceDiagram
    participant H as 硬件传感器
    participant MQ as RabbitMQ
    participant L as AlarmListener
    participant E as SensorDataExecutor
    participant W as WaveService
    participant I as InfluxDB
    
    H->>MQ: 发送数据(id="TEMP_001", command="acceleration", axis=1)
    MQ->>L: 消息路由
    L->>E: 处理数据
    E->>W: 查询Wave(sensorCode="TEMP_001", sampleDataType="acceleration", measureDirection=1)
    W->>E: 返回Wave(id=1001)
    E->>I: 存储数据(monitorId, waveId=1001, data)
```

## 7. 特殊传感器的关联

### 7.1 应力波传感器

```java
// 应力波传感器有编号区分
MessageBean stressMessage = new MessageBean()
    .setId("STRESS_001")         // 传感器编码
    .setCommand("acceleration")   // 数据类型
    .setNumber("1")              // 应力波编号
    .setOriginTime(System.currentTimeMillis());

// 对应的Wave查询
Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
    .eq(Wave::getSensorCode, "STRESS_001")
    .eq(Wave::getSampleDataType, "acceleration")
    .eq(Wave::getNumber, "1"));  // 编号匹配
```

### 7.2 电流传感器

```java
// 电流传感器有相位区分
MessageBean currentMessage = new MessageBean()
    .setId("CURRENT_001")        // 传感器编码
    .setCommand("current")       // 数据类型
    .setPhase("A")              // A相
    .setOriginTime(System.currentTimeMillis());

// 对应的Wave查询
Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
    .eq(Wave::getSensorCode, "CURRENT_001")
    .eq(Wave::getSampleDataType, "current")
    .eq(Wave::getPhase, "A"));   // 相位匹配
```

## 8. 数据存储结构

### 8.1 InfluxDB存储

```java
// 数据存储时的标识
influxdbTools.insert(
    wave.getMonitorId().toString(),  // 数据库名：监测点ID
    wave.getId().toString(),         // 表名：波形ID
    data,                           // 数据内容
    originTime                      // 时间戳
);

// 存储结构示例
// 数据库：monitor_123456
// 表：wave_1001 (X轴加速度)
// 表：wave_1002 (Y轴加速度)  
// 表：wave_1003 (温度)
```

### 8.2 SensorData实体

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/SensorData.java" mode="EXCERPT">
````java
@TableName("sidas_sensor_data")
public class SensorData implements Serializable {
    /**
     * 波形id - 系统内部标识
     */
    private Long waveId;
    
    /**
     * 传感器实例编码 - 硬件关联标识
     */
    private String sensorCode;
    
    /**
     * 采样数据类型
     */
    private String sampleDataType;
    
    /**
     * 测量方向
     */
    private Integer measureDirection;
    
    /**
     * 采集时间
     */
    private Date originTime;
    
    /**
     * 数据值
     */
    private BigDecimal value;
}
````
</augment_code_snippet>

## 9. 关联机制优势

### 9.1 设计优势

1. **解耦合**：硬件编码与系统ID分离，便于维护
2. **多维匹配**：支持复杂传感器的精确识别
3. **扩展性**：易于添加新的传感器类型
4. **容错性**：支持传感器重新配置和替换

### 9.2 查询效率

```java
// 系统内部查询使用waveId（高效）
List<SensorData> data = influxdbTools.queryData(startTime, endTime, waveId);

// 硬件关联查询使用sensorCode（转换后使用waveId）
Wave wave = waveService.getBySensorCode(sensorCode, sampleDataType, measureDirection);
List<SensorData> data = influxdbTools.queryData(startTime, endTime, wave.getId());
```

## 10. 故障排查

### 10.1 常见问题

1. **找不到对应Wave**：检查sensorCode、sampleDataType、measureDirection匹配
2. **数据类型错误**：确认command与Wave的sampleDataType一致
3. **轴向映射错误**：检查axis与measureDirection的转换逻辑

### 10.2 调试方法

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public MonitorDTO basicInfoBySensorCode(String sensorCode) {
    if (Func.isEmpty(sensorCode)) {
        log.error("传感器编码为空，无法查询设备和部位信息");
        return null;
    }

    // 根据传感器编码查找传感器实例
    SensorInstance sensorInstance = sensorInstanceService.getOne(
        Wrappers.<SensorInstance>query().lambda()
            .eq(SensorInstance::getCode, sensorCode));
    
    if (sensorInstance == null) {
        log.error("未找到传感器实例，sensorCode = {}", sensorCode);
        return null;
    }

    // 查找对应的监测点
    Monitor monitor = monitorService.getById(sensorInstance.getMonitorId());
    if (monitor == null) {
        log.error("未找到监测点，monitorId = {}", sensorInstance.getMonitorId());
        return null;
    }
    
    return MonitorWrapper.build().entityDTO(monitor);
}
````
</augment_code_snippet>

## 11. 总结

### 11.1 关联机制核心

- **硬件标识**：传感器编码（sensorCode）
- **系统标识**：波形ID（waveId）
- **转换桥梁**：Wave表的多维度匹配
- **数据流向**：硬件 → sensorCode → Wave查询 → waveId → 数据处理

### 11.2 关键特点

1. **双重标识**：硬件编码与系统ID并存
2. **精确匹配**：多维度条件确保准确关联
3. **灵活扩展**：支持各种类型传感器
4. **高效处理**：系统内部使用数字ID提高性能

**结论**：硬件传感器与系统的关联主要通过传感器编码（sensorCode）实现，系统接收到数据后通过多维度匹配找到对应的Wave，然后使用waveId进行内部数据处理和存储。这种设计既保证了硬件的灵活性，又确保了系统的高效性。
