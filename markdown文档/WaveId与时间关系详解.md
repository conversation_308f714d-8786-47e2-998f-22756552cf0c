# WaveId与时间关系详解

## 1. 概述

**WaveId本身不包含时间信息**，但在异常生成和数据处理过程中，时间是一个关键的维度参数。WaveId与时间的关系主要体现在数据查询、异常判断和缓存机制中。

## 2. WaveId的时间无关性

### 2.1 WaveId的静态特性

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/Wave.java" mode="EXCERPT">
````java
@TableName("sidas_wave")
public class Wave implements Serializable {
    /**
     * 主键 - 波形ID（静态标识符）
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    
    /**
     * 波形名称（静态配置）
     */
    private String waveName;
    
    /**
     * 测点id（静态关联）
     */
    private Long monitorId;
    
    // 注意：Wave实体中没有时间字段
    // WaveId是一个静态的配置标识符
}
````
</augment_code_snippet>

**关键特点**：
- WaveId是一个**静态标识符**，不随时间变化
- 一个WaveId代表一个固定的数据采集通道配置
- WaveId本身不包含任何时间信息

## 3. 时间在数据流中的作用

### 3.1 传感器数据的时间维度

<augment_code_snippet path="szyk-zbusiness/zbusiness-api/src/main/java/com/snszyk/zbusiness/basic/entity/SensorData.java" mode="EXCERPT">
````java
@TableName("sidas_sensor_data")
public class SensorData implements Serializable {
    /**
     * 波形id（关联到Wave）
     */
    private Long waveId;
    
    /**
     * 采集时间（关键时间字段）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date originTime;
    
    /**
     * 数据值
     */
    private BigDecimal value;
    
    /**
     * 报警等级
     */
    private Integer alarmLevel;
}
````
</augment_code_snippet>

### 3.2 数据关系图

```mermaid
graph TD
    A[WaveId: 1001] --> B[SensorData 1]
    A --> C[SensorData 2]
    A --> D[SensorData 3]
    A --> E[SensorData N]
    
    B --> B1[originTime: 2024-01-01 10:00:00]
    C --> C1[originTime: 2024-01-01 10:01:00]
    D --> D1[originTime: 2024-01-01 10:02:00]
    E --> E1[originTime: 2024-01-01 10:N:00]
    
    B --> B2[value: 2.5]
    C --> C2[value: 3.1]
    D --> D2[value: 4.2]
    E --> E2[value: X.X]
```

## 4. 时间在异常生成中的关键作用

### 4.1 Redis缓存Key的时间组合

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AlarmLogicService.java" mode="EXCERPT">
````java
public synchronized void setRedisData(String type, Long waveId, Date originTime, Integer value) {
    log.info("设置REDIS标志位：波形：{}，类型：{}，时间：{}", waveId, type, originTime.getTime());
    // 关键：WaveId + 时间戳 + 类型 组成唯一Key
    String key = waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + type;
    szykRedis.setEx(key, value, Duration.ofMinutes(5));
}
````
</augment_code_snippet>

**Redis Key格式**：
```
{waveId}:{timestamp}:{type}
例如：1001:1704067200000:threshold
```

### 4.2 时间窗口的重要性

时间在异常生成中起到**时间窗口定位**的作用：

```mermaid
timeline
    title WaveId数据时间线
    
    section 10:00:00
        正常数据 : WaveId=1001 : value=2.0
    
    section 10:01:00
        超限数据 : WaveId=1001 : value=5.0 : 触发门限报警
    
    section 10:02:00
        AI诊断 : WaveId=1001 : AI异常检测
    
    section 10:03:00
        机理诊断 : WaveId=1001 : 机理模型报警
    
    section 10:04:00
        异常生成 : 基于10:01:00时间点 : 生成异常记录
```

## 5. 连续策略中的时间窗口

### 5.1 连续策略时间查询

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
private AbnormalRecordVO continuousThresholdWeight(Long waveId, Date originTime) {
    // 查询指定时间点之前的连续数据
    List<JSONObject> list = influxdbTools.queryData(null, originTime.getTime() + 1000L, jsonObject, (query) -> {
        query.addSort("_time", true);
        query.getQuerySQL().append("|> limit(n:" + continuousTimes + ", offset:0)");
    });
    
    // 获取这些时间点的报警记录
    List<Date> originTimeList = sensorDataList.stream()
        .map(s -> s.getOriginTime())
        .collect(Collectors.toList());
    
    List<AlarmRecord> alarmRecordList = alarmRecordService.list(
        Wrappers.<AlarmRecord>query().lambda()
            .eq(AlarmRecord::getWaveId, waveId)
            .in(AlarmRecord::getAlarmTime, originTimeList)
    );
}
````
</augment_code_snippet>

### 5.2 连续策略时间逻辑

```mermaid
graph TD
    A[当前时间点: originTime] --> B[向前查询N个数据点]
    B --> C[获取这N个时间点]
    C --> D[查询这些时间点的报警记录]
    D --> E{连续N次都报警?}
    E -->|是| F[生成异常记录]
    E -->|否| G[不生成异常]
    
    A1[示例: 10:05:00] --> B1[查询前6个数据点]
    B1 --> C1[10:00:00, 10:01:00, 10:02:00, 10:03:00, 10:04:00, 10:05:00]
    C1 --> D1[检查这6个时间点是否都有报警]
    D1 --> E1{6次连续报警?}
    E1 -->|是| F1[权重=累加权重×6]
    E1 -->|否| G1[权重=0]
```

## 6. 非连续策略中的时间范围

### 6.1 非连续策略时间查询

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
private AbnormalRecordVO discontinuousThresholdWeight(Long waveId, Date originTime) {
    // 查询指定时间范围内的报警记录
    List<AlarmRecord> alarmRecordList = alarmRecordService.list(
        Wrappers.<AlarmRecord>query().lambda()
            .eq(AlarmRecord::getWaveId, waveId)
            .eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.THRESHOLD.getCode())
            .between(AlarmRecord::getAlarmTime, 
                DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), 
                originTime)
    );
    
    // 3天内，报警次数大于等于80次
    if (alarmRecordList.size() >= alarmTimes) {
        abnormalRecord.setWeight(maxWeight);
        return abnormalRecord;
    }
}
````
</augment_code_snippet>

### 6.2 非连续策略时间逻辑

```mermaid
graph TD
    A[当前时间点: originTime] --> B[计算时间范围]
    B --> C[startTime = originTime - statisticsDays]
    C --> D[查询时间范围内的报警记录]
    D --> E{报警次数 >= 阈值?}
    E -->|是| F[权重=最大权重]
    E -->|否| G[权重=0]
    
    A1[示例: 2024-01-04 10:00:00] --> B1[统计天数: 3天]
    B1 --> C1[开始时间: 2024-01-01 10:00:00]
    C1 --> D1[查询3天内的报警记录]
    D1 --> E1{报警次数 >= 80?}
    E1 -->|是| F1[权重=100]
    E1 -->|否| G1[权重=0]
```

## 7. 时间在异常生成中的具体应用

### 7.1 异常生成时间参数

<augment_code_snippet path="szyk-zbusiness/zbusiness-ops/src/main/java/com/snszyk/zbusiness/ops/service/logic/AbnormalLogicService.java" mode="EXCERPT">
````java
public void alarmDataHandler(String tenantId, Long waveId, Date originTime) {
    // originTime是触发异常生成的关键时间点
    // 所有的权重计算都基于这个时间点进行
    
    // 1. 连续策略：以originTime为终点，向前查询
    AbnormalRecordVO thresholdRecord = this.continuousThresholdWeight(waveId, originTime);
    
    // 2. 非连续策略：以originTime为终点，向前统计时间范围
    AbnormalRecordVO discontinuousRecord = this.discontinuousThresholdWeight(waveId, originTime);
    
    // 3. 生成异常时使用originTime作为异常时间
    if (weightSum >= strategy.getWeightSum()) {
        this.addAbnormal(tenantId, originTime, detailList);
    }
}
````
</augment_code_snippet>

### 7.2 时间参数的传递链路

```mermaid
sequenceDiagram
    participant S as SensorData
    participant A as AlarmLogicService
    participant R as Redis
    participant AB as AbnormalLogicService
    
    S->>A: handleSensorData(sensorData.originTime)
    A->>R: setRedisData(waveId, originTime, value)
    Note over R: Key: waveId:timestamp:type
    A->>AB: alarmDataHandler(tenantId, waveId, originTime)
    AB->>AB: 基于originTime计算权重
    AB->>AB: 生成异常记录(originTime)
```

## 8. 时间精度和同步

### 8.1 时间精度要求

- **毫秒级精度**：`originTime.getTime()` 返回毫秒时间戳
- **Redis Key唯一性**：时间戳确保同一WaveId在不同时间点的唯一性
- **数据一致性**：所有相关操作使用相同的originTime

### 8.2 时间同步机制

```java
// 示例：确保时间一致性
Date originTime = sensorData.getOriginTime();

// 1. 设置Redis标志位
setRedisData("threshold", waveId, originTime, 2);

// 2. 保存报警记录
alarmRecord.setAlarmTime(originTime);

// 3. 触发异常生成
alarmDataHandler(tenantId, waveId, originTime);

// 4. 生成异常详情
abnormalDetail.setAbnormalTime(originTime);
```

## 9. 关键时间概念总结

### 9.1 时间类型

1. **originTime（采集时间）**：传感器数据的实际采集时间
2. **alarmTime（报警时间）**：报警记录的生成时间
3. **abnormalTime（异常时间）**：异常记录的生成时间
4. **createTime（创建时间）**：数据库记录的创建时间

### 9.2 时间关系

```mermaid
graph LR
    A[originTime] --> B[alarmTime]
    B --> C[abnormalTime]
    C --> D[createTime]
    
    A1[数据采集] --> B1[报警判断]
    B1 --> C1[异常生成]
    C1 --> D1[记录保存]
```

## 10. 总结

### 10.1 核心要点

1. **WaveId本身不包含时间信息**，是静态的配置标识符
2. **时间是数据处理的关键维度**，用于定位具体的数据点
3. **Redis缓存使用WaveId+时间戳**组合确保唯一性
4. **异常生成基于特定时间点**进行权重计算和判断
5. **连续策略和非连续策略**都依赖时间窗口进行计算

### 10.2 时间的作用

- **数据定位**：通过时间精确定位数据点
- **窗口计算**：基于时间窗口进行统计分析
- **缓存管理**：时间戳确保缓存Key的唯一性
- **异常追溯**：记录异常发生的具体时间点

**结论**：WaveId与时间是**正交关系** - WaveId标识"什么数据通道"，时间标识"什么时候的数据"，两者结合才能唯一确定一个具体的数据点和相关的异常判断。
