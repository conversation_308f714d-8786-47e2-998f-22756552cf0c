package com.snszyk.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 平台logo实体类
 *
 * <AUTHOR>
 * @since 2023/5/10 15:07
 **/
@Data
@TableName("szyk_logo")
@ApiModel(value = "Logo对象", description = "Logo对象")
public class Logo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 平台名称
	 */
	@ApiModelProperty(value = "平台名称")
	private String platformName;
	/**
	 * 平台简称
	 */
	@ApiModelProperty(value = "平台简称")
	private String shortName;
	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;

	/**
	 * logo（附件表id）
	 */
	@ApiModelProperty(value = "logo（附件表id）")
	private Long attachId;

	/**
	 * 是否已删除
	 */
	@TableLogic
	private Integer isDeleted;
}
