<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.snszyk</groupId>
    <artifactId>szyk-system</artifactId>
    <version>2.0.0.RELEASE</version>
  </parent>
  <groupId>com.snszyk</groupId>
  <artifactId>system-rest</artifactId>
  <version>2.0.0.RELEASE</version>
  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-common</artifactId>
      <version>2.0.0.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>system-api</artifactId>
      <version>2.0.0.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-core-boot</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>szyk-core-cloud</artifactId>
          <groupId>com.snszyk</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-http</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-api-crypto</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-datascope</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-develop</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-swagger</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-social</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-spring-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-core-auto</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-core-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-report</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.whvcse</groupId>
      <artifactId>easy-captcha</artifactId>
    </dependency>
    <dependency>
      <groupId>org.flowable</groupId>
      <artifactId>flowable-spring-boot-starter</artifactId>
      <version>${flowable.version}</version>
    </dependency>
    <dependency>
      <groupId>org.flowable</groupId>
      <artifactId>flowable-json-converter</artifactId>
      <version>${flowable.version}</version>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-generator</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>com.snszyk</groupId>
      <artifactId>szyk-starter-sms</artifactId>
    </dependency>
    <dependency>
      <groupId>io.minio</groupId>
      <artifactId>minio</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aliyun</groupId>
      <artifactId>aliyun-java-sdk-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.qcloud</groupId>
      <artifactId>cos_api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.qcloudsms</groupId>
      <artifactId>qcloudsms</artifactId>
    </dependency>
    <dependency>
      <groupId>com.qiniu</groupId>
      <artifactId>qiniu-java-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.yunpian.sdk</groupId>
      <artifactId>yunpian-java-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
      </resource>
    </resources>
  </build>
</project>
