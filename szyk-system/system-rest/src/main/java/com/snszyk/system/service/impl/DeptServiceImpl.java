/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.TreeNode;
import com.snszyk.core.tool.utils.*;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.User;
import com.snszyk.system.entity.UserDept;
import com.snszyk.system.mapper.DeptMapper;
import com.snszyk.system.mapper.UserDeptMapper;
import com.snszyk.system.mapper.DeptMapper;
import com.snszyk.system.mapper.UserMapper;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.vo.*;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.system.vo.DeptVO;
import com.snszyk.system.wrapper.DeptWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.snszyk.core.tool.node.ForestNodeMerger.merge;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements IDeptService {
	private static final String TENANT_ID = "tenantId";
	private static final String PARENT_ID = "parentId";
	private final UserMapper userMapper;
	private final UserDeptMapper userDeptMapper;

	@Override
	public List<DeptVO> lazyList(String tenantId, Long parentId, Map<String, Object> param) {
		// 设置租户ID
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		String paramTenantId = Func.toStr(param.get(TENANT_ID));
		if (Func.isNotEmpty(paramTenantId) && AuthUtil.isAdministrator()) {
			tenantId = paramTenantId;
		}
		// 判断点击搜索但是没有查询条件的情况
		if (Func.isEmpty(param.get(PARENT_ID)) && param.size() == 1) {
			parentId = 0L;
		}
		// 判断数据权限控制,非超管角色只可看到本级及以下数据
		if (Func.toLong(parentId) == 0L && !AuthUtil.isAdministrator()) {
			Long deptId = Func.firstLong(AuthUtil.getDeptId());
			Dept dept = SysCache.getDept(deptId);
			if (dept.getParentId() != 0) {
				parentId = dept.getParentId();
			}
		}
		// 判断点击搜索带有查询条件的情况
		if (Func.isEmpty(param.get(PARENT_ID)) && param.size() > 1 && Func.toLong(parentId) == 0L) {
			parentId = null;
		}
		return baseMapper.lazyList(tenantId, parentId, param);
	}

	@Override
	public List<DeptVO> tree(String tenantId) {
		return merge(baseMapper.tree(tenantId));
	}

	@Override
	public List<DeptVO> lazyTree(String tenantId, Long parentId) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		return merge(baseMapper.lazyTree(tenantId, parentId));
	}

	@Override
	public String getDeptIds(String tenantId, String deptNames) {
		List<Dept> deptList = baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId).in(Dept::getDeptName, Func.toStrList(deptNames)));
		if (deptList != null && deptList.size() > 0) {
			return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public String getDeptIdsByFuzzy(String tenantId, String deptNames) {
		LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId);
		queryWrapper.and(wrapper -> {
			List<String> names = Func.toStrList(deptNames);
			names.forEach(name -> wrapper.like(Dept::getDeptName, name).or());
		});
		List<Dept> deptList = baseMapper.selectList(queryWrapper);
		if (deptList != null && deptList.size() > 0) {
			return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public List<String> getDeptNames(String deptIds) {
		return baseMapper.getDeptNames(Func.toLongArray(deptIds));
	}

	@Override
	public List<Dept> getDeptChild(Long deptId) {
		return baseMapper.selectList(Wrappers.<Dept>query().lambda().like(Dept::getAncestors, deptId));
	}

	@Override
	public boolean removeDept(String ids) {
		Integer cnt = baseMapper.selectCount(Wrappers.<Dept>query().lambda().in(Dept::getParentId, Func.toLongList(ids)));
		if (cnt > 0) {
			throw new ServiceException("请先删除子节点!");
		}
		return removeByIds(Func.toLongList(ids));
	}

	@Override
	public boolean submit(Dept dept) {
		if (Func.isEmpty(dept.getParentId())) {
			dept.setTenantId(AuthUtil.getTenantId());
			dept.setParentId(SzykConstant.TOP_PARENT_ID);
			dept.setAncestors(String.valueOf(SzykConstant.TOP_PARENT_ID));
			dept.setAncestorName(SzykConstant.TOP_PARENT_NAME);
		}
		if (dept.getParentId() > 0) {
			Dept parent = getById(dept.getParentId());
			if (Func.toLong(dept.getParentId()) == Func.toLong(dept.getId())) {
				throw new ServiceException("父节点不可选择自身!");
			}
			dept.setTenantId(parent.getTenantId());
			String ancestors = parent.getAncestors() + StringPool.COMMA + dept.getParentId();
			dept.setAncestors(ancestors);
			String ancestorName = parent.getAncestorName() + StringPool.DASH + parent.getDeptName();
			dept.setAncestorName(ancestorName);
		}
		dept.setIsDeleted(SzykConstant.DB_NOT_DELETED);
		Dept oldDept = null;
		if (dept.getId() != null) {
			oldDept = baseMapper.selectById(dept.getId());
		}

		boolean update = saveOrUpdate(dept);

		if (dept.getId() != null && update) {
			updateChildrenAncestorName(dept, oldDept);
		}
		return update;
	}

	/**
	 * 更新子部门的祖级名称
	 * @param dept 父部门
	 * @return
	 */
	private boolean updateChildrenAncestorName(Dept dept, Dept oldDept) {
		if (oldDept == null || dept.getDeptName().equals(oldDept.getDeptName())) {
			return true;
		}

		//查询所有子部门 & 更新AncestorName
		QueryWrapper<Dept> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().like(Dept::getAncestors, dept.getId())
			.eq(Dept::getIsDeleted, SzykConstant.DB_NOT_DELETED);
		List<Dept> childrenList = baseMapper.selectList(queryWrapper);
		if (CollectionUtil.isNotEmpty(childrenList)) {
			childrenList.forEach(childDept -> {
				QueryWrapper<Dept> ancestorQueryWrapper = new QueryWrapper<>();
				ancestorQueryWrapper.lambda().in(Dept::getId, Func.toLongList(childDept.getAncestors()))
					.eq(Dept::getIsDeleted, SzykConstant.DB_NOT_DELETED);
				List<Dept> ancestorDeptList = baseMapper.selectList(ancestorQueryWrapper);

				List<String> ancestorNameList = ancestorDeptList.stream().map(Dept::getDeptName).collect(Collectors.toList());
				ancestorNameList.add(0, SzykConstant.TOP_PARENT_NAME);
				String ancestorName = StringUtil.join(ancestorNameList, StringPool.DASH);

				childDept.setAncestorName(ancestorName);
				baseMapper.updateById(childDept);
			});
		}

		return true;
	}

	@Override
	public List<DeptVO> search(String deptName, Long parentId) {
		String tenantId = AuthUtil.getTenantId();
		LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda();
		if (Func.isNotEmpty(tenantId)) {
			queryWrapper.eq(Dept::getTenantId, tenantId);
		}
		if (Func.isNotEmpty(deptName)) {
			queryWrapper.like(Dept::getDeptName, deptName);
		}
		if (Func.isNotEmpty(parentId) && parentId > 0L) {
			queryWrapper.eq(Dept::getParentId, parentId);
		}
		List<Dept> deptList = baseMapper.selectList(queryWrapper);
		return DeptWrapper.build().listNodeVO(deptList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public DelResultVO checkAndRemoveDept(List<Long> ids) {
		DelResultVO resultVO = new DelResultVO();
		ids.stream().forEach(id -> {
			//查询机构信息
			Dept dept = this.getById(id);
			//如果机构不存在，直接失败返回
			if (dept == null) {
				resultVO.getDetailVOList().add(new DelDetailVO(id.toString(), Boolean.FALSE, "机构不存在"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在子节点，不允许删除
			Integer childDeptCnt = baseMapper.selectCount(Wrappers.<Dept>query().lambda().in(Dept::getParentId, Arrays.asList(id)));
			if (childDeptCnt > 0) {
				resultVO.getDetailVOList().add(new DelDetailVO(dept.getDeptName(), Boolean.FALSE, "存在子节点，不允许删除!"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在用户引用，不允许删除
			List<User> refs = userMapper.selectList(Wrappers.<User>query().lambda().like(User::getDeptId, id).eq(User::getIsDeleted, 0));
			if (CollectionUtil.isEmpty(refs)) {
				//如果无用户引用，则删除此机构
				this.removeById(id);
				resultVO.getDetailVOList().add(new DelDetailVO(dept.getDeptName(), Boolean.TRUE, "删除成功"));
				//成功次数+1
				resultVO.setSuccessNumber(resultVO.getSuccessNumber() + 1);
			} else {
				//存在用户引用不允许删除，收集引用的用户名称放入失败提示信息
				Set<String> refNameSet = refs.stream().map(User::getRealName).collect(Collectors.toSet());
				resultVO.getDetailVOList().add(new DelDetailVO(dept.getDeptName(), Boolean.FALSE, "机构信息被用户引用，无法删除，具体如下：\r\n"
					+ StringUtil.collectionToDelimitedString(refNameSet, ",")));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
			}
		});
		return resultVO;
	}

	@Override
	public List<DeptAttilaVO> attilaTree(String tenantId) {
		List<DeptAttilaVO> result = new ArrayList<>();

		// 查询所有组织
		List<TreeNode> list = baseMapper.treeDept(tenantId);
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		for (TreeNode treeNode : list) {
			DeptAttilaVO attilaVO = new DeptAttilaVO();
			BeanUtil.copyProperties(treeNode, attilaVO);
			attilaVO.setDataType(1);
			result.add(attilaVO);
		}
		List<DeptAttilaVO> results = merge(result);
		forSetChildren(results);
		return results;
	}

	public void forSetChildren(List<DeptAttilaVO> result) {
		for (DeptAttilaVO vo : result) {
			if (CollectionUtils.isEmpty(vo.getChildren())) {
				vo.setHasChildren(false);
			} else {
				vo.setHasChildren(true);
				forSetChildren(vo.getChildren());
			}
		}
	}


	@Override
	public List<DeptUserAttilaVO> attilaTreeUser(String tenantId) {
		List<DeptUserAttilaVO> result = new ArrayList<>();

		// 查询顶级节点组织
		LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda();
		queryWrapper.eq(Dept::getParentId, 0);
		queryWrapper.eq(Dept::getIsDeleted, 0);

		List<Dept> list = baseMapper.selectList(queryWrapper);
		if (CollectionUtils.isEmpty(list)) {
			return result;
		}

		for (Dept dept : list) {

			DeptUserAttilaVO vo = new DeptUserAttilaVO();
			vo.setId(dept.getId());
			vo.setParentId(dept.getParentId());
			vo.setDataType(1);
			vo.setLabel(dept.getDeptName());
			vo.setUserId(null);
			vo.setHasChildren(false);

			List<DeptUserAttilaVO> childrenList = new ArrayList<>();

			// 查询下级部门
			LambdaQueryWrapper<Dept> deptWrapper = Wrappers.<Dept>query().lambda();
			deptWrapper.eq(Dept::getParentId, dept.getId());
			deptWrapper.eq(Dept::getIsDeleted, 0);

			List<Dept> deptList = baseMapper.selectList(deptWrapper);
			if (!CollectionUtils.isEmpty(deptList)) {
				vo.setHasChildren(true);
				setDeptList(dept, deptList, childrenList);
			}

			// 查询部门下用户
			LambdaQueryWrapper<UserDept> userDeptWrapper = Wrappers.<UserDept>query().lambda();
			userDeptWrapper.eq(UserDept::getDeptId, dept.getId());

			List<UserDept> userDeptList = userDeptMapper.selectList(userDeptWrapper);
			if (!CollectionUtils.isEmpty(userDeptList)) {

				List<Long> userIds = userDeptList.stream().map(UserDept::getUserId).collect(Collectors.toList());

				// 查询用户数据
				LambdaQueryWrapper<User> userWrapper = Wrappers.<User>query().lambda();
				userWrapper.in(User::getId, userIds);
				userWrapper.eq(User::getIsDeleted, 0);

				List<User> userList = userMapper.selectList(userWrapper);
				if (!CollectionUtils.isEmpty(userList)) {
					vo.setHasChildren(true);
					setUserList(dept, userList, childrenList);
				}
			}
			vo.setChildren(childrenList);

			result.add(vo);
		}


		return result;
	}

	@Override
	public List<DeptLinkVO> selectLinkList(Long orgId) {
		if (Func.isEmpty(orgId)) {
			orgId = Long.parseLong(AuthUtil.getDeptId());
		}

		List<DeptLinkVO> result = new ArrayList<>();

		// 当前节点组织
		LambdaQueryWrapper<Dept> wrapper = Wrappers.<Dept>query().lambda();
		wrapper.eq(Dept::getId, orgId);
		wrapper.eq(Dept::getIsDeleted, 0);

		Dept dept = baseMapper.selectOne(wrapper);

		if (dept == null) {
			return result;
		}
		result.add(new DeptLinkVO(dept.getId().toString(), dept.getDeptName()));

		// 查询上级节点组织
		LambdaQueryWrapper<Dept> superiorWrapper = Wrappers.<Dept>query().lambda();
		superiorWrapper.eq(Dept::getId, dept.getParentId());
		superiorWrapper.eq(Dept::getIsDeleted, 0);

		Dept superiorDept = baseMapper.selectOne(superiorWrapper);

		if (superiorDept != null) {
			result.add(new DeptLinkVO(superiorDept.getId().toString(), superiorDept.getDeptName()));
		}

		// 查询上级节点组织
		LambdaQueryWrapper<Dept> lowerWrapper = Wrappers.<Dept>query().lambda();
		lowerWrapper.eq(Dept::getParentId, dept.getId());
		lowerWrapper.eq(Dept::getIsDeleted, 0);

		List<Dept> lowerList = baseMapper.selectList(lowerWrapper);
		if (!CollectionUtils.isEmpty(lowerList)) {
			lowerList.forEach(item -> {
				result.add(new DeptLinkVO(item.getId().toString(), item.getDeptName()));
			});
		}

		return result;
	}

	@Override
	public DeptVO getDeptByUserId(Long userId) {
		LambdaQueryWrapper<UserDept> queryWrapper = Wrappers.<UserDept>query().lambda();
		queryWrapper.eq(UserDept::getUserId, userId);
		queryWrapper.eq(StringUtils.isNotBlank(AuthUtil.getDeptId()), UserDept::getDeptId, AuthUtil.getDeptId());

		UserDept userDept = userDeptMapper.selectOne(queryWrapper);

		if (userDept == null) {
			return null;
		}

		LambdaQueryWrapper<Dept> deptWrapper = Wrappers.<Dept>query().lambda();
		deptWrapper.eq(Dept::getId, userDept.getDeptId());
		deptWrapper.eq(Dept::getIsDeleted, 0);

		Dept dept = baseMapper.selectOne(deptWrapper);
		if (dept == null) {
			return null;
		}
		DeptVO deptVO = new DeptVO();
		BeanUtil.copyProperties(dept, deptVO);

		return deptVO;
	}

	public void setDeptList(Dept parentDept, List<Dept> list, List<DeptUserAttilaVO> result) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (Dept dept : list) {

			DeptUserAttilaVO vo = new DeptUserAttilaVO();
			vo.setId(dept.getId());
			vo.setParentId(parentDept.getId());
			vo.setParentName(parentDept.getDeptName());
			vo.setDataType(1);
			vo.setLabel(dept.getDeptName());
			vo.setUserId(null);
			vo.setHasChildren(false);

			List<DeptUserAttilaVO> childrenList = new ArrayList<>();

			// 查询下级部门
			LambdaQueryWrapper<Dept> deptWrapper = Wrappers.<Dept>query().lambda();
			deptWrapper.eq(Dept::getParentId, dept.getId());
			deptWrapper.eq(Dept::getIsDeleted, 0);

			List<Dept> deptList = baseMapper.selectList(deptWrapper);
			if (!CollectionUtils.isEmpty(deptList)) {
				vo.setHasChildren(true);
				setDeptList(dept, deptList, childrenList);
			}

			// 查询部门下用户
			LambdaQueryWrapper<UserDept> userDeptWrapper = Wrappers.<UserDept>query().lambda();
			userDeptWrapper.eq(UserDept::getDeptId, dept.getId());

			List<UserDept> userDeptList = userDeptMapper.selectList(userDeptWrapper);
			if (!CollectionUtils.isEmpty(userDeptList)) {

				List<Long> userIds = userDeptList.stream().map(UserDept::getUserId).collect(Collectors.toList());

				// 查询用户数据
				LambdaQueryWrapper<User> userWrapper = Wrappers.<User>query().lambda();
				userWrapper.in(User::getId, userIds);
				userWrapper.eq(User::getIsDeleted, 0);

				List<User> userList = userMapper.selectList(userWrapper);
				if (!CollectionUtils.isEmpty(userList)) {
					vo.setHasChildren(true);
					setUserList(dept, userList, childrenList);
				}
			}
			vo.setChildren(childrenList);

			result.add(vo);
		}

	}

	public void setUserList(Dept parentDept, List<User> list, List<DeptUserAttilaVO> result) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (User user : list) {

			DeptUserAttilaVO vo = new DeptUserAttilaVO();
			vo.setId(user.getId());
			vo.setUserId(user.getId());
			vo.setParentId(parentDept.getId());
			vo.setParentName(parentDept.getDeptName());
			vo.setDataType(3);
			vo.setLabel(user.getName());
			vo.setAvatar(user.getAvatar());
			vo.setHasChildren(false);

			vo.setChildren(new ArrayList<>());
			result.add(vo);
		}

	}


	/**
	 * 获取所有部门列表
	 * @param tenantId 租户id
	 * @return
	 */
	@Override
	public List<DeptVO> allDeptList(String tenantId) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		return baseMapper.allDeptList(tenantId);
	}

	@Override
	public List<DeptVO> lazyTreeByParent(String tenantId, Long parentId) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		return ForestNodeMerger.merge(baseMapper.lazyTreeByParent(tenantId, parentId));
	}

	@Override
	public List<String> selectAncestorIdList(String tenantId, String deptName) {
		if (StringUtil.isEmpty(deptName)) {
			return Collections.emptyList();
		}

		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}

		QueryWrapper<Dept> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda()
			.eq(StringUtil.isNotBlank(tenantId), Dept::getTenantId, tenantId)
			.like(Dept::getDeptName, deptName)
			.select(Dept::getId, Dept::getAncestors);
		List<Dept> deptList = baseMapper.selectList(queryWrapper);

		List<String> ancestorIdList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(deptList)) {
			deptList.forEach(dept -> {
				String ancestors = dept.getAncestors();
				List<String> ancestorList = Func.toStrList(ancestors);
				ancestorIdList.addAll(ancestorList);
			});
		} else {
			return Collections.emptyList();
		}

		return ancestorIdList.stream().distinct().collect(Collectors.toList());
	}

	@Override
	public List<String> selectAncestorIdListByIds(String tenantId, List<Long> ids) {
		if(CollectionUtil.isEmpty(ids)) {
			return Collections.emptyList();
		}

		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}

		QueryWrapper<Dept> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda()
			.eq(StringUtil.isNotBlank(tenantId), Dept::getTenantId, tenantId)
			.in(Dept::getId, ids)
			.select(Dept::getId, Dept::getAncestors);
		List<Dept> deptList = baseMapper.selectList(queryWrapper);

		List<String> ancestorIdList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(deptList)) {
			deptList.forEach(dept -> {
				String ancestors = dept.getAncestors();
				List<String> ancestorList = Func.toStrList(ancestors);
				ancestorIdList.addAll(ancestorList);
			});
		} else {
			return Collections.emptyList();
		}

		return ancestorIdList.stream().distinct().collect(Collectors.toList());
	}
}
