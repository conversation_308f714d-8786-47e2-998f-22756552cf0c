/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.excel;

import lombok.RequiredArgsConstructor;
import com.snszyk.core.excel.support.ExcelImporter;
import com.snszyk.system.service.IUserService;

import java.util.List;

/**
 * 用户数据导入类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class UserImporter implements ExcelImporter<UserExcel> {

	private final IUserService service;
	private final Boolean isCovered;

	@Override
	public void save(List<UserExcel> data) {
		service.importUser(data, isCovered);
	}
}
