/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.system.entity.UserOauth;
import lombok.AllArgsConstructor;
import com.snszyk.system.mapper.UserOauthMapper;
import com.snszyk.system.service.IUserOauthService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserOauthServiceImpl extends ServiceImpl<UserOauthMapper, UserOauth> implements IUserOauthService {

}
