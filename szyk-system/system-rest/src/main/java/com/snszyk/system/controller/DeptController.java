/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.controller;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.cache.CacheNames;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.system.cache.*;
import com.snszyk.common.enums.DictEnum;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.secure.constant.AuthConstant;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.DictCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.vo.*;
import com.snszyk.system.wrapper.DeptWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/dept")
@Api(value = "部门", tags = "部门")
@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
public class DeptController extends SzykController {

	private final IDeptService deptService;

	private final SzykRedis szykRedis;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入dept")
	public R<DeptVO> detail(Dept dept) {
		Dept detail = deptService.getOne(Condition.getQueryWrapper(dept));
		return R.data(DeptWrapper.build().entityVO(detail));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入dept")
	public R<List<DeptVO>> list(@ApiIgnore @RequestParam Map<String, Object> dept, SzykUser szykUser) {
		QueryWrapper<Dept> queryWrapper = Condition.getQueryWrapper(dept, Dept.class);
		List<Dept> list = deptService.list((!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Dept::getTenantId, szykUser.getTenantId()) : queryWrapper);
		return R.data(DeptWrapper.build().listNodeVO(list));
	}

	/**
	 * 懒加载列表
	 */
	@GetMapping("/lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入dept")
	public R<List<DeptVO>> lazyList(@ApiIgnore @RequestParam Map<String, Object> dept, Long parentId, SzykUser szykUser) {
		List<DeptVO> list = deptService.lazyList(szykUser.getTenantId(), parentId, dept);
		return R.data(DeptWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 获取部门树形结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<DeptVO>> tree(String tenantId, SzykUser szykUser) {
		List<DeptVO> tree = deptService.tree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 懒加载获取部门树形结构
	 */
	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<DeptVO>> lazyTree(String tenantId, Long parentId, SzykUser szykUser) {
		List<DeptVO> tree = deptService.lazyTree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()), parentId);
		return R.data(tree);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入dept")
	public R submit(@Valid @RequestBody Dept dept) {
		if (deptService.submit(dept)) {
			CacheUtil.clear(SYS_CACHE);
			//创建一个单线程的线程池
			ExecutorService executor = ExecutorBuilder.create()
				.setCorePoolSize(1)
				.setMaxPoolSize(1)
				.setKeepAliveTime(0)
				.build();
			executor.submit(() -> DeptCache.setList(dept.getTenantId()));

			// 返回懒加载树更新节点所需字段
			Kv kv = Kv.create().set("id", String.valueOf(dept.getId())).set("tenantId", dept.getTenantId())
				.set("deptCategoryName", DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory()));
			return R.data(kv);
		}
		return R.fail("操作失败");
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(deptService.removeDept(ids));
	}

	/**
	 * 校验并删除
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "校验并删除", notes = "传入ids")
	public R<DelResultVO> checkAndRemove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, SzykUser szykUser) {
		DelResultVO delResultVO = deptService.checkAndRemoveDept(Func.toLongList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(delResultVO);
		result.setSuccess(delResultVO.getFailureNumber() == 0);

		//创建一个单线程的线程池
		ExecutorService executor = ExecutorBuilder.create()
			.setCorePoolSize(1)
			.setMaxPoolSize(1)
			.setKeepAliveTime(0)
			.build();
		executor.submit(() -> DeptCache.setList(szykUser.getTenantId()));
		return result;
	}

	/**
	 * 下拉数据源
	 */
	@PreAuth(AuthConstant.PERMIT_ALL)
	@GetMapping("/select")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "下拉数据源", notes = "传入id集合")
	public R<List<Dept>> select(Long userId, String deptId) {
		if (Func.isNotEmpty(userId)) {
			User user = UserCache.getUser(userId);
			deptId = user.getDeptId();
		}
		List<Dept> list = deptService.list(Wrappers.<Dept>lambdaQuery().in(Dept::getId, Func.toLongList(deptId)));
		return R.data(list);
	}

	/**
	 * 所有部门
	 */
	@GetMapping("/all")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "获取所有部门")
	public R<List<DeptVO>> all(@ApiParam(value = "租户id", required = true) @RequestParam String tenantId) {
		List<DeptVO> result = DeptCache.getList(tenantId);
		return R.data(result);
	}

	/**
	 * 懒加载获取部门树形结构
	 */
	@GetMapping("/lazy-tree-by-parent")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<DeptVO>> lazyTreeByParent(String tenantId, Long parentId, SzykUser szykUser) {
		List<DeptVO> tree = deptService.lazyTreeByParent(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()), parentId);
		return R.data(tree);
	}

	/**
	 * 根据部门名称模糊搜索所有父级部门的id列表
	 * @param deptName 部门名称
	 * @return
	 */
	@GetMapping("/select-ancestor-id-list")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "获取所有父级部门id列表", notes = "传入租户id、部门名称")
	public R<List<String>> selectAncestorIdList(String tenantId, String deptName) {
		List<String> result = deptService.selectAncestorIdList(tenantId, deptName);
		return R.data(result);
	}

	/**
	 * 根据部门id列表搜索所有父级部门的id列表
	 * @param ids 部门id列表，用英文逗号分隔
	 * @return
	 */
	@GetMapping("/select-ancestor-id-list-by-ids")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "获取所有父级部门id列表", notes = "传入租户id、部门id列表(用英文逗号分隔)")
	public R<List<String>> selectAncestorIdListByIds(String tenantId, String ids) {
		List<String> result = deptService.selectAncestorIdListByIds(tenantId, Func.toLongList(ids));
		return R.data(result);
	}

	/**
	 * 部门选择列表
	 */
	@GetMapping("/select-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "部门选择列表", notes = "传入dept")
	public R<List<DeptVO>> selectList(@ApiIgnore @RequestParam Map<String, Object> dept, SzykUser szykUser) {
		QueryWrapper<Dept> queryWrapper = Condition.getQueryWrapper(dept, Dept.class);
		List<Dept> list = deptService.list((!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ?
			queryWrapper.lambda().eq(Dept::getTenantId, szykUser.getTenantId()) : queryWrapper);
		return R.data(BeanUtil.copy(list, DeptVO.class));
	}

}
