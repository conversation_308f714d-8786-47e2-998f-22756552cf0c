/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.engine.constant;

/**
 * 流程常量.
 *
 * <AUTHOR>
 */
public interface FlowEngineConstant {

	String FLOWABLE_BASE_PACKAGES = "org.flowable.ui";

	String SUFFIX = ".bpmn20.xml";

	String ACTIVE = "active";

	String SUSPEND = "suspend";

	String STATUS_TODO = "todo";

	String STATUS_CLAIM = "claim";

	String STATUS_SEND = "send";

	String STATUS_DONE = "done";

	String STATUS_FINISHED = "finished";

	String STATUS_UNFINISHED = "unfinished";

	String STATUS_FINISH = "finish";

	String START_EVENT = "startEvent";

	String END_EVENT = "endEvent";

}
