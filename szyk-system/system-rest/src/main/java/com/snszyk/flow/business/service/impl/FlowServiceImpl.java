/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.business.service.impl;

import lombok.AllArgsConstructor;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.flow.business.service.IFlowService;
import com.snszyk.flow.core.entity.SzykFlow;
import com.snszyk.flow.core.utils.TaskUtil;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 流程实现类
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
public class FlowServiceImpl implements IFlowService {

	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final TaskService taskService;

	@Override
	public SzykFlow startProcessInstanceById(String processDefinitionId, String businessKey, Map<String, Object> variables) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, businessKey, variables);
		// 组装流程通用类
		SzykFlow flow = new SzykFlow();
		flow.setProcessInstanceId(processInstance.getId());
		return flow;
	}

	@Override
	public SzykFlow startProcessInstanceByKey(String processDefinitionKey, String businessKey, Map<String, Object> variables) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
		// 组装流程通用类
		SzykFlow flow = new SzykFlow();
		flow.setProcessInstanceId(processInstance.getId());
		return flow;
	}

	@Override
	public boolean completeTask(String taskId, String processInstanceId, String comment, Map<String, Object> variables) {
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, comment);
		}
		// 非空判断
		if (Func.isEmpty(variables)) {
			variables = Kv.create();
		}
		// 完成任务
		taskService.complete(taskId, variables);
		return true;
	}

	@Override
	public Object taskVariable(String taskId, String variableName) {
		return R.data(taskService.getVariable(taskId, variableName));
	}

	@Override
	public Map<String, Object> taskVariables(String taskId) {
		return taskService.getVariables(taskId);
	}

}
