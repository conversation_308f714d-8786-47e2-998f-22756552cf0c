/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.builder.sms;

import com.github.qcloudsms.SmsMultiSender;
import lombok.SneakyThrows;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.sms.SmsTemplate;
import com.snszyk.core.sms.props.SmsProperties;
import com.snszyk.core.sms.TencentSmsTemplate;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Sms;

/**
 * 腾讯云短信构建类
 *
 * <AUTHOR>
 */
public class TencentSmsBuilder {

	@SneakyThrows
	public static SmsTemplate template(Sms sms, SzykRedis szykRedis) {
		SmsProperties smsProperties = new SmsProperties();
		smsProperties.setTemplateId(sms.getTemplateId());
		smsProperties.setAccessKey(sms.getAccessKey());
		smsProperties.setSecretKey(sms.getSecretKey());
		smsProperties.setSignName(sms.getSignName());
		SmsMultiSender smsSender = new SmsMultiSender(Func.toInt(smsProperties.getAccessKey()), sms.getSecretKey());
		return new TencentSmsTemplate(smsProperties, smsSender, szykRedis);
	}

}
