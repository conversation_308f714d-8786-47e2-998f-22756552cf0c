package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 传感器实例表
 * <AUTHOR>
 */
@Data
@TableName("basic_sensor_instance")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "传感器实例表")
public class SensorInstance extends BaseEntity {

	/**
	 * 传感器类型ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("传感器类型ID")
	private Long typeId;

	/**
	 * 设备ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("设备ID")
	private Long equipmentId;

	/**
	 * 测点ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("测点ID")
	private Long monitorId;

	/**
	 * 安装方向（0:轴向，1:水平，2:垂直），仅温振一体传感器
	 */
	@ApiModelProperty(value = "安装方向（0:轴向，1:水平，2:垂直），仅温振一体传感器")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer installDirection;

	/**
	 * 编号，仅应力波传感器
	 */
	@ApiModelProperty(value = "编号，仅应力波传感器")
	private String number;

	/**
	 * 相位（A、B、C），仅电流传感器
	 */
	@ApiModelProperty(value = "相位（A、B、C），仅电流传感器")
	private String phase;

	/**
	 * 采集站ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("采集站ID")
	private Long stationId;

	/**
	 * 唯一识别码
	 */
	@ApiModelProperty(value = "唯一识别码")
	private String code;

	/**
	 * 是否点检仪类型：0-普通传感器；1-点检仪类型传感器。
	 */
	@ApiModelProperty(value = "是否点检仪类型：0-普通传感器；1-点检仪类型传感器。")
	private Integer isSpotCheck;

	/**
	 * 单值采样时间间隔（S）
	 */
	@ApiModelProperty(value = "单值采样时间间隔（S）")
	private Integer singleSampleInterval;

	/**
	 * 波形采样时间间隔（S）
	 */
	@ApiModelProperty(value = "波形采样时间间隔（S）")
	private Integer waveSampleInterval;

	/**
	 * 3D模型传感器id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "3D模型传感器id")
	private String virtualSensorId;

	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
