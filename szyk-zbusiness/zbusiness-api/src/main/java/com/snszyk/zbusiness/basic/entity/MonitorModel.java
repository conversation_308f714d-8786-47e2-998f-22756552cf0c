/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 部位机理模型表实体类
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
@Data
@Accessors(chain = true)
@TableName("eolm_monitor_model")
@ApiModel(value = "MonitorModel对象", description = "部位机理模型表")
public class MonitorModel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 机理模型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "机理模型id")
	private Long modelId;
	/**
	 * 机理模型个性化内容
	 */
	@ApiModelProperty(value = "机理模型个性化内容")
	private String modelInfo;
	/**
	 * 应用状态（0,：正常，1：停用）
	 */
	@ApiModelProperty(value = "应用状态（0：正常，1：停用）")
	private Integer status;

}
