package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.entity.Monitor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @ClassName: EquipmentMessageVO
 * @author: wangmh
 * @create: 2022-12-13 09:27
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentMessageVO {

	/**
	 * 设备信息
	 */
	private Equipment equipment;

	/**
	 * 测点信息
	 */
	private List<Monitor> monitorList;
}
