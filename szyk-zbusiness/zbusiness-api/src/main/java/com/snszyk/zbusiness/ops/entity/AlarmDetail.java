/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 报警管理明细表实体类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Data
@Accessors(chain = true)
@TableName("eolm_alarm_detail")
@ApiModel(value = "AlarmDetail对象", description = "报警管理明细表")
public class AlarmDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 报警id
	 */
	@ApiModelProperty(value = "报警id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long alarmId;

	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("波形id")
	private Long waveId;

	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long equipmentId;
	/**
	 * 报警点
	 */
	@ApiModelProperty(value = "报警点")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long monitorId;
	/**
	 * 报警点名称
	 */
	@ApiModelProperty(value = "报警点名称")
	private String monitorName;
	/**
	 * 测点路径
	 */
	@ApiModelProperty(value = "测点路径")
	private String monitorPath;
	/**
	 * 报警类型（字典：alarm_biz_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警类型（字典：alarm_biz_type）")
	private Integer alarmType;
	/**
	 * 报警等级（字典：alarm_level）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警等级（字典：alarm_level）")
	private Integer alarmLevel;
	/**
	 * 报警指标
	 */
	@ApiModelProperty(value = "报警指标")
	private String alarmIndex;
	/**
	 * 报警值
	 */
	@ApiModelProperty(value = "报警值")
	private BigDecimal val;
	/**
	 * 报警值单位
	 */
	@ApiModelProperty(value = "报警值单位")
	private String unit;
	/**
	 * 首次报警时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "首次报警时间")
	private Date firstAlarmTime;
	/**
	 * 最新报警时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "最新报警时间")
	private Date lastAlarmTime;
	/**
	 * 报警数据时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "报警数据时间")
	private Date alarmDataTime;
	/**
	 * 诊断结论
	 */
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	/**
	 * 报警次数
	 */
	@ApiModelProperty(value = "报警次数")
	private Integer times;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
