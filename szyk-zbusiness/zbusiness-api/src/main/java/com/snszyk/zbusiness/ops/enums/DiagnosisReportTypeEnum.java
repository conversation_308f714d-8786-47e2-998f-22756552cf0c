/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 诊断报告类型枚举类
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Getter
@AllArgsConstructor
public enum DiagnosisReportTypeEnum {

	/**
	 * 月度诊断报告
	 */
	MONTHLY_DIAGNOSIS_REPORT(1, "月度诊断报告"),
	;

    /**
     * 编码
     */
    final Integer code;

    /**
     * 名称
     */
    final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举实例
     */
    public static DiagnosisReportTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DiagnosisReportTypeEnum value : DiagnosisReportTypeEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
