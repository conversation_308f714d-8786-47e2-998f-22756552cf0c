/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 3D模型配置表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@Accessors(chain = true)
public class SensorModelConfigDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 部位路径
	 */
	@ApiModelProperty(value = "部位路径")
	private String monitorPath;

	/**
	 * 传感器名称
	 */
	@ApiModelProperty(value = "传感器名称")
	private String sensorName;

	/**
	 * 传感器型号
	 */
	@ApiModelProperty(value = "传感器型号")
	private String sensorModel;

	/**
	 * 传感器编码
	 */
	@ApiModelProperty(value = "传感器编码")
	private String sensorCode;

	/**
	 * 虚拟传感器id
	 */
	@ApiModelProperty(value = "虚拟传感器id")
	private String virtualSensorId;

	/**
	 * 实时数据列表
	 */
	@ApiModelProperty(value = "实时数据列表")
	private List<RealTimeDataDTO> realTimeDataList;

	/**
	 * 2D图数据展示框位置
	 */
	@ApiModelProperty(value = "2D图数据展示框位置")
	private String displayPosition;

	/**
	 * 最大报警等级
	 */
	@ApiModelProperty(value = "最大报警等级")
	private Integer maxAlarmLevel;

	public SensorModelConfigDTO(){

	}

	public SensorModelConfigDTO(String sensorCode, String virtualSensorId){
		this.sensorCode = sensorCode;
		this.virtualSensorId = virtualSensorId;
	}

}
