//package com.snszyk.zbusiness.ai.dto;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.time.LocalDateTime;
//
///**
// * 消息数据传输对象
// */
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//public class AiMessageDto {
//    private String id;
//    private String content;
//    private String type;
//    private LocalDateTime timestamp;
//
//    public static AiMessageDto of(String content, String type) {
//        return AiMessageDto.builder()
//                .id(java.util.UUID.randomUUID().toString())
//                .content(content)
//                .type(type)
//                .timestamp(LocalDateTime.now())
//                .build();
//    }
//}
