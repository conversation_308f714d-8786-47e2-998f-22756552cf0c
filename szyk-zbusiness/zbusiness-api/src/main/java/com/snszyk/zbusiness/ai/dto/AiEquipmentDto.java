package com.snszyk.zbusiness.ai.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AiEquipmentDto {

	private Long id;

	/**
	 * 父主键
	 */
	private Long deviceId;
	/**
	 * 编码
	 */
	private String code;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 型号
	 */
	private String model;
	/**
	 * 频率
	 */
	private BigDecimal frequency;
	/**
	 * 转速
	 */
	private BigDecimal rev;
	/**
	 * 照片
	 */
	private Long image;
	/**
	 * 设备等级,1:A级关键，2:B级重要，3:C级一般
	 */
	private Integer grade;
	/**
	 * 分类（业务字典：设备分类）
	 */
	private Integer category;
	/**
	 * 设备类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）
	 */
	private Integer type;
	/**
	 * 设备功率
	 */
	private BigDecimal power;
	/**
	 * 故障状态
	 */
	private Integer isFault;
	/**
	 * 3D场景id
	 */
	private Long sceneId;
	/**
	 * 制造厂商
	 */
	private String manufacturer;
	/**
	 * 生产工艺
	 */
	private Integer produceTech;
	/**
	 * 预期寿命（天）
	 */
	private Integer lifeExpectancy;
	/**
	 * 开始使用日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date startDateOfUse;
	/**
	 * RFID标签
	 */
	private String rfid;
	/**
	 * 生产日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date productionDate;
	/**
	 * 是否需要点检：0-不需要，1-需要
	 */
	private Integer needSpotCheck;
	/**
	 * 运行状态：0-停机；1-运行中。
	 */
	private Integer isRunning;
	/**
	 * 最新运行时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date originRuntime;
	/**
	 * 运行时长
	 */
	private Long runningTime;
	/**
	 * 停机时长
	 */
	private Long shutdownTime;
	/**
	 * 本次运行时长
	 */
	private Long currentRuntime;
	/**
	 * 全路径
	 */
	private String path;
	/**
	 * 全路径名称
	 */
	private String pathName;
	/**
	 * 排序
	 */
	private Integer sort;
	/**
	 * 点检状态
	 */
	private Integer inspectStatus;

	/**
	 * 测点列表
	 */
	private List<AiMonitorDto> monitorList;


	public StringBuilder convertLLmPrompt(){
		StringBuilder b = new StringBuilder();
		b.append("1.设备信息：")
		 .append("<br>")
		 .append("&emsp;&emsp;设备名称：")
		 .append(this.name)
		 .append("，设备编码：")
		 .append(this.code)
		 .append("，设备型号：")
		 .append(this.model)
		 .append("，设备转速：")
		 .append(this.rev)
		 .append("，设备频率：")
		 .append(this.frequency)
		 .append("，设备功率：")
		 .append(this.power);
		return b;
	}
}
