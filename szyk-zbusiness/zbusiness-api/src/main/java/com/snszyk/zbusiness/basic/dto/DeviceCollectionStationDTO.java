/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.DeviceCollectionStation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class DeviceCollectionStationDTO extends DeviceCollectionStation {

	private static final long serialVersionUID = 1L;

	/**
	 * 采集站名称
	 */
	@ApiModelProperty(value = "采集站名称")
	private String stationName;

	/**
	 * 是否在线：0-离线；1-在线
	 */
	@ApiModelProperty(value = "是否在线：0-离线；1-在线")
	private Integer online;

	/**
	 * 是否在线
	 */
	@ApiModelProperty(value = "是否在线")
	private String onlineName;

}
