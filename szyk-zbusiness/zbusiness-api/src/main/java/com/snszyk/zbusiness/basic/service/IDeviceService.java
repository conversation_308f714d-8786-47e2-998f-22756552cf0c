/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.basic.dto.DeviceCoordinateSubDTO;
import com.snszyk.zbusiness.basic.dto.DeviceDTO;
import com.snszyk.zbusiness.basic.entity.Device;

import java.util.List;

/**
 * 设备树表 服务类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
public interface IDeviceService extends BaseService<Device> {

	/**
	 * 获取子位置列表
	 *
	 * @param deviceId
	 * @return
	 */
	List<Device> getDeviceChild(Long deviceId);

	/**
	 * 删除设备树
	 *
	 * @param ids
	 * @return
	 */
	boolean removeDevice(List<Long> ids);

	/**
	 * 校验同级是否同名
	 *
	 * @param device
	 * @return
	 */
	boolean isNameExist(Device device);

	/**
	 * 校验编码唯一性
	 *
	 * @param device
	 * @return
	 */
	boolean isCodeDuplicate(Device device);

	/**
	 * 校验并删除设备点位
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveDevice(List<Long> ids);

	/**
	 * 查询门户区域点位视图列表
	 *
	 * @param id 区域id
	 * @return
	 */
	List<DeviceCoordinateSubDTO> deviceView(Long id);

	/**
	 * 根据编码查询
	 *
	 * @param code
	 * @return
	 */
	DeviceDTO getByCode(String code);

	/**
	 * 根据层级查询
	 *
	 * @param level
	 * @return
	 */
	List<DeviceDTO> getByLevel(Integer level);

	/**
	 * 获取子级
	 *
	 * @param parentId
	 * @return
	 */
	List<DeviceDTO> getByParentId(Long parentId);

	/**
	 * 查询device表当前最大sort值
	 *
	 * @return
	 */
	Integer getMaxSort();

	List<Device> listSecondLevelDevice(String tenantId);
}
