/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import com.snszyk.zbusiness.ops.entity.AlarmThreshold;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 报警门限表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CommonThresholdDTO extends AlarmThreshold {
	private static final long serialVersionUID = 1L;

	/**
	 * 测点路径
	 */
	@ApiModelProperty(value = "测点路径")
	private String pathName;
	/**
	 * 测点名称
	 */
	@ApiModelProperty(value = "测点名称")
	private String monitorName;

}
