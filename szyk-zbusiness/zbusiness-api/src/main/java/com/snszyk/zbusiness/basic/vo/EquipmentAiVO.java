/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.EquipmentAi;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备AI模型表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-07-08
 */
@Data
@ApiModel(value = "EquipmentAiVO对象", description = "EquipmentAiVO对象")
public class EquipmentAiVO extends EquipmentAi {
	private static final long serialVersionUID = 1L;

	/**
	 * 父主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父主键")
	private Long parentId;

}
