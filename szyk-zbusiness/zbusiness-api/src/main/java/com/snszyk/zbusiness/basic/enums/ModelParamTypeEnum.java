/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机理模型阈值类型 枚举类
 *
 * <AUTHOR>
 * @date 2024/01/05 16:35
 **/
@Getter
@AllArgsConstructor
public enum ModelParamTypeEnum {

	/**
	 * 正常
	 */
	NORMAL(0, "正常"),

	/**
	 * 注意
	 */
	NOTICE(1, "注意"),

	/**
	 * 预警
	 */
	WARNING(2, "预警"),

	/**
	 * 报警
	 */
	ALARM(3, "报警"),

	/**
	 * 危险
	 */
	DANGER(4, "危险"),
	;

	final Integer code;
	final String name;

	public static ModelParamTypeEnum getByCode(Integer code){
		for (ModelParamTypeEnum value : ModelParamTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
