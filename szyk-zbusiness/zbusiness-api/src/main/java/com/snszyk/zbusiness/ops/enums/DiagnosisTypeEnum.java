/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 诊断类型枚举类
 *
 * <AUTHOR>
 * @date 2022/12/15 15:10
 **/
@Getter
@AllArgsConstructor
public enum DiagnosisTypeEnum {

	/**
	 * 智能诊断
	 */
	INTELLIGENCE(0, "智能诊断"),
	/**
	 * 专家诊断
	 */
	EXPERT(1, "专家诊断"),
	;

	final Integer code;
	final String name;

	public static DiagnosisTypeEnum getByCode(Integer code){
		for (DiagnosisTypeEnum value : DiagnosisTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
