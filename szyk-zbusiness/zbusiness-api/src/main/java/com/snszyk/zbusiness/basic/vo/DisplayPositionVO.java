/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.DisplayPosition;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟展示位置表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DisplayPositionVO对象", description = "虚拟展示位置表")
public class DisplayPositionVO extends DisplayPosition {
	private static final long serialVersionUID = 1L;

}
