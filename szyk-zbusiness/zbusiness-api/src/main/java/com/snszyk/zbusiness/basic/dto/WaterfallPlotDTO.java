package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 瀑布图数据
 *
 * <AUTHOR>
 * @date 2023/04/18 17:30
 **/
@Data
@Accessors(chain = true)
public class WaterfallPlotDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 采样数据数组
	 */
	@ApiModelProperty(value = "采样数据数组")
	private List<List<BigDecimal>> dataList;

	/**
	 * 采样时间数组
	 */
	@ApiModelProperty(value = "采样时间数组")
	private List<Date> dateList;

}
