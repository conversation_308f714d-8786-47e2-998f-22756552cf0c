/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.zbusiness.basic.dto.TreeDTO;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.vo.BasicTreeVO;

import java.util.List;
import java.util.Map;

/**
 * 基础树表 服务类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface IBasicTreeService extends IService<BasicTree> {

	/**
	 * 懒加载基础树列表
	 *
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<BasicTreeVO> lazyList(Long parentId, Map<String, Object> param);

	/**
	 * 树形结构
	 *
	 * @param tenantId
	 * @return
	 */
	List<BasicTreeVO> tree(String tenantId);

	/**
	 * 懒加载树形结构
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<BasicTreeVO> lazyTree(String tenantId, Long parentId);

	/**
	 * 获取基础树ID
	 *
	 * @param tenantId
	 * @param nodeNames
	 * @return
	 */
	String getNodeIds(String tenantId, String nodeNames);

	/**
	 * 获取基础树ID
	 *
	 * @param tenantId
	 * @param nodeNames
	 * @return
	 */
	String getNodeIdsByFuzzy(String tenantId, String nodeNames);

	/**
	 * 获取基础树节点名
	 *
	 * @param nodeIds
	 * @return
	 */
	List<String> getNodeNames(String nodeIds);

	/**
	 * 获取子节点
	 *
	 * @param nodeId
	 * @return
	 */
	List<BasicTree> getNodeChild(Long nodeId);

	/**
	 * 获取所有的父节点
	 *
	 * @param nodeId
	 * @return
	 */
	List<BasicTree> getNodeParent(Long nodeId);

	/**
	 * 获取所有的父节点（包含自己本身）
	 *
	 * @param tenantId
	 * @param param
	 * @return
	 */
	List<BasicTree> getNodeParent(String tenantId, Map<String, Object> param);

	/**
	 * 提交
	 *
	 * @param basicTreeVO
	 * @return
	 */
	String submit(BasicTreeVO basicTreeVO);

	/**
	 * 地点下拉树
	 *
	 * @param parentId
	 * @return
	 */
	List<BasicTreeVO> selectDeviceTree(Long parentId);

	/**
	 * 驾驶舱设备树
	 *
	 * @param hasTop
	 * @param szykUser
	 * @return
	 */
	List<BasicTreeVO> cockpitDeviceTree(Integer hasTop, SzykUser szykUser);

	/**
	 * 业务筛选树节点列表
	 *
	 * @param tenantId
	 * @param param
	 * @return
	 */
	List<BasicTree> bizFilterList(String tenantId, Map<String, Object> param);

	List<TreeDTO> areaList();
}
