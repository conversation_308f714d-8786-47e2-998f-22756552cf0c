/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备测点参数-齿轮信息视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@ApiModel(value = "ParamGearVO对象", description = "设备测点参数-齿轮信息")
public class ParamGearVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 齿轮名称
	 */
	@ApiModelProperty(value = "齿轮名称")
	private String gearName;
	/**
	 * 叶片信息
	 */
	@ApiModelProperty(value = "齿轮齿数")
	private Integer gearTeeth;
	/**
	 * 齿轮转速
	 */
	@ApiModelProperty(value = "齿轮转速")
	private BigDecimal gearSpeed;
	/**
	 * 齿轮啮合频率
	 */
	@ApiModelProperty(value = "齿轮啮合频率")
	private BigDecimal meshFreq;

}
