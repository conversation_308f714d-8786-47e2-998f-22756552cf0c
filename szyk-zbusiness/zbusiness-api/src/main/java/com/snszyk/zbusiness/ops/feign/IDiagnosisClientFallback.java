///*
// *      Copyright (c) 2018-2088
// */
//package com.snszyk.zbusiness.ops.feign;
//
//import com.snszyk.core.tool.api.R;
//import com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO;
//import com.snszyk.zbusiness.ops.vo.BearingVO;
//import com.snszyk.zbusiness.ops.vo.DiagnosisRecordVO;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * Feign失败配置
// *
// * <AUTHOR>
// */
//@Component
//public class IDiagnosisClientFallback implements IDiagnosisClient {
//
//	@Override
//	public R<List<DiagnosisRecordVO>> recordsByDeviceId(Long deviceId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<DiagnosticReportDTO> diagnosticReportByEquipment(Long equipmentId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<BearingVO> bearingInfoById(Long bearingId) {
//		return R.fail("获取数据失败");
//	}
//
//}
