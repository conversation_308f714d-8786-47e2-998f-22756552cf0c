/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 机理模型实体类
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Data
@Accessors(chain = true)
@TableName("eolm_mechanism_model")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MechanismModel对象", description = "机理模型")
public class MechanismModel extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	 * 描述
	 */
	@ApiModelProperty(value = "描述")
	private String description;
	/**
	 * 应用设备类型（字典：device_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用设备类型（字典：device_type）")
	private Integer applyEquipment;
	/**
	 * 应用功率范围（字典：power_range）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用功率范围（字典：power_range）")
	private Integer applyPower;
	/**
	 * 机理类型（字典：model_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "机理类型（字典：model_type）")
	private Integer type;
	/**
	 * 应用数据类型（字典：sampled_data_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用数据类型（字典：sampled_data_type）")
	private Integer applyData;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
