/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警门限-类型枚举类
 *
 * <AUTHOR>
 * @date 2023/03/15 09:56
 **/
@Getter
@AllArgsConstructor
public enum ThresholdTypeEnum {

	/**
	 * 非振动指标
	 */
	NON_VIBRATION_QUOTA(0, "非振动指标"),
	/**
	 * 通用指标
	 */
	COMMON_QUOTA(1, "通用指标"),
	/**
	 * 采样值指标
	 */
	SAMPLE_VALUE(2, "采样值指标"),
	/**
	 * 其他指标
	 */
	OTHER_QUOTA(3, "其他指标"),

	;

	final Integer code;
	final String name;

	public static ThresholdTypeEnum getByCode(Integer code){
		for (ThresholdTypeEnum value : ThresholdTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
