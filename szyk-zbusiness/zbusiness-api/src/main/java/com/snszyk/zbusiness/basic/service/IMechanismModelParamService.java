/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.entity.MechanismModelParam;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;

/**
 * 机理模型参数表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
public interface IMechanismModelParamService extends IService<MechanismModelParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mechanismModelParam
	 * @return
	 */
	IPage<MechanismModelParamVO> selectMechanismModelParamPage(IPage<MechanismModelParamVO> page, MechanismModelParamVO mechanismModelParam);

}
