package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.SensorData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 传感器数据表DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SensorDataDTO extends SensorData {

	private static final long serialVersionUID = 1L;

	/**
	 * 是否异常
	 */
	@ApiModelProperty("是否异常")
	private String invalidName;

	public String getInvalidName() {
		if (this.getInvalid() == null) {
			return null;
		}
		return this.getInvalid() == 1 ? "是" : "否";
	}

	/**
	 * 传感器类型
	 */
	@ApiModelProperty(value = "传感器类型")
	private Integer category;

	/**
	 * 传感器类型
	 */
	@ApiModelProperty(value = "传感器类型")
	private String categoryName;

	/**
	 * 采样数据类型
	 */
	@ApiModelProperty(value = "采样数据类型")
	private String sampleDataTypeName;

}
