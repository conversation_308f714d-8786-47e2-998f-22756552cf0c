/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.ai.dto.AiEquipmentAlarmDto;
import com.snszyk.zbusiness.ai.dto.AiEquipmentDto;
import com.snszyk.zbusiness.ai.dto.AiMonitorDto;
import com.snszyk.zbusiness.ai.dto.AiWaveDto;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.vo.BindRfidVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningMonitorVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningStatVO;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
public interface IEquipmentService extends BaseService<Equipment> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<EquipmentDTO> page(IPage<EquipmentDTO> page, EquipmentVO vo);

	/**
	 * 校验并删除设备
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveEquipment(List<Long> ids);

	/**
	 * 校验编码唯一性
	 *
	 * @param equipment
	 * @return
	 */
	boolean isCodeDuplicate(Equipment equipment);

	/**
	 * 分页统计设备运行情况
	 *
	 * @param page     分页
	 * @param vo       vo
	 * @param tenantId 租户
	 * @return
	 */
	IPage<EquipmentRunningStatExcelDTO> equipmentRunningPage(IPage<EquipmentRunningStatExcelDTO> page, EquipmentRunningStatVO vo, String tenantId);

	/**
	 * 设备统计
	 *
	 * @param tenantId 租户
	 * @return
	 */
	EquipmentStatDTO equipmentStat(String tenantId);

	/**
	 * 设备引用AI模型
	 *
	 * @param id
	 * @param aiModelIds
	 * @return
	 */
	boolean bindAiModel(Long id, String aiModelIds);

	/**
	 * 设备解除引用AI模型
	 *
	 * @param id
	 * @param aiModelId
	 * @return
	 */
	boolean unbindAiModel(Long id, Long aiModelId);

	/**
	 * 获取指定设备同category（同tenantId）的设备id
	 *
	 * @param equipmentId 设备id
	 * @return
	 */
	List<Long> getIdListOfSameCategory(Long equipmentId);

	/**
	 * 返回设备表当前最大sort值
	 *
	 * @return
	 */
	Integer getMaxSort();

	/**
	 * 重置设备的needSpotCheck为0
	 *
	 * @param equipmentIdList 设备id列表
	 * @return
	 */
	int resetEquipmentNeedSpotCheck(List<Long> equipmentIdList);

	/**
	 * 绑定rfid
	 *
	 * @param vo vo
	 * @return
	 */
	boolean bindRfid(BindRfidVO vo);

	/**
	 * 根据rfid获取设备信息
	 *
	 * @param rfid rfid
	 * @return
	 */
	EquipmentDTO detailByRfid(String rfid);

	/**
	 * 查询设备的累计报警、故障数
	 *
	 * @param equipmentId 设备id
	 * @return
	 */
	AbnormalTimesDTO abnormalTimes(Long equipmentId);

	/**
	 * 根据monitorId获取设备信息
	 *
	 * @param monitorId monitorId
	 * @return
	 */
	Equipment getByMonitorId(Long monitorId);


	/**
	 * 异常设备统计
	 *
	 * @return
	 */
	AbnormalEquipmentTotal abnormalEquipmentTotal();

	/**
	 * 重点关注设备
	 *
	 * @return
	 */
	List<ImportantEquipment> importantEquipment(Long id);

	/**
	 * 设备故障类型统计
	 *
	 * @return
	 */
	List<EquipmentTypeTotal> equipmentTypeTotal(Long equipmentId);


	/**
	 * 设备频发次数统计
	 *
	 * @return
	 */
	List<EquipmentFrequency> equipmentFrequency();

	/**
	 * 异常状态分布统计
	 *
	 * @return
	 */
	List<AbnormalScatter> abnormalScatter(Long id);

	/**
	 * 设备运行监控
	 *
	 * @param equipment
	 * @return
	 */
	List<EquipmentRunningMonitorDTO> equipmentRunningMonitor(IPage<EquipmentRunningMonitorDTO> page, EquipmentRunningMonitorVO equipment);

	List<EquipmentMonitorAbnormalDTO> equipmentMonitorAbnormal(@Param("equipmentId") Long equipmentId);


	/**
	 * 设备台帐查询
	 *
	 * @param page
	 * @param equipment
	 * @return
	 */
	List<EquipmentDTO> equipmentLedger(IPage<EquipmentDTO> page, EquipmentVO equipment);


	Integer count(String tenantId, String path);

	EquipmentStatDTO equipmentStatistics();

	AiEquipmentDto getEquipmentInfo(@Param("equipmentId") Long equipmentId);

	AiEquipmentDto getEquipmentByMonitorId(@Param("monitorId") Long monitorId);

	List<AiMonitorDto> getMonitorList(@Param("equipmentId") Long equipmentId);

	List<AiWaveDto> getWaveList(@Param("monitorId") Long monitorId);

	List<AiWaveDto> waveList(@Param("monitorId") Long monitorId);

	AiEquipmentAlarmDto equipmentAlarm(@Param("equipmentId") Long equipmentId);

}
