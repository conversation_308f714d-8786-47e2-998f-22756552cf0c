/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备过滤枚举类
 *
 * <AUTHOR>
 * @date 2025/05/01 10:56
 **/
@Getter
@AllArgsConstructor
public enum EquipmentFilterEnum {

	/**
	 * 重点关注设备
	 */
	FOCUS(0, "重点关注设备"),

	/**
	 * 全部设备
	 */
	ALL(1, "全部"),
	;

	final Integer code;
	final String name;

	/**
	 * 根据编码获取枚举值
	 *
	 * @param code 编码
	 * @return 枚举值
	 */
	public static EquipmentFilterEnum getByCode(Integer code){
		for (EquipmentFilterEnum value : EquipmentFilterEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
