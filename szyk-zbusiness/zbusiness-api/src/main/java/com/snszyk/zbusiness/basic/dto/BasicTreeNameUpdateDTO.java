package com.snszyk.zbusiness.basic.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 生产结构树节点（地点、设备、测点）名称更新
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BasicTreeNameUpdateDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 地点
	 */
	public static final Integer TYPE_DEVICE = 1;

	/**
	 * 设备
	 */
	public static final Integer TYPE_EQUIPMENT = 2;

	/**
	 * 测点
	 */
	public static final Integer TYPE_MONITOR = 3;

	/**
	 * 节点id
	 */
	private Long id;

	/**
	 * 节点最新名称
	 */
	private String newName;

	/**
	 * 节点最新路径名称
	 */
	private String newPathName;

	/**
	 * 节点类型：1 - 地点；2 - 设备；3 - 测点
	 */
	private Integer type;

}
