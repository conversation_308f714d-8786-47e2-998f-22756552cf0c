package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 轴承特征频率
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BearingCharacteristicFreqDTO对象", description = "轴承特征频率")
public class BearingCharacteristicFreqDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 外圈特征频率
	 */
	@ApiModelProperty(value = "外圈特征频率")
	private BigDecimal bpfo;

	/**
	 * 内圈特征频率
	 */
	@ApiModelProperty(value = "内圈特征频率")
	private BigDecimal bpfi;

	/**
	 * 保持架的特征频率
	 */
	@ApiModelProperty(value = "保持架的特征频率")
	private BigDecimal ftf;

	/**
	 * 滚动体的特征频率
	 */
	@ApiModelProperty(value = "滚动体的特征频率")
	private BigDecimal bsf;
}
