/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.common.CrudPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 诊断报告表实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AiopsDiagnosisReportPageVo对象", description = "诊断报告表分页查询")
public class AiopsDiagnosisReportPageVo extends CrudPage {

	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;


	/**
	 * 发布开始日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "发布开始日期")
	private LocalDate publishStartDate;

	/**
	 * 发布结束日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "发布结束日期")
	private LocalDate publishEndDate;


	@ApiModelProperty(value = "报告名称")
	private  String title;

}
