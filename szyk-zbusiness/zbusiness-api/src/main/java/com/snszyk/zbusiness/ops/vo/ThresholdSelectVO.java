/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 报警门限选择下拉列表视图实体类
 *
 * <AUTHOR>
 * @date 2024/02/01 09:06
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "ThresholdSelectVO对象", description = "报警门限选择下拉列表")
public class ThresholdSelectVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 报警类型
	 */
	@ApiModelProperty(value = "报警类型")
	private Integer alarmType;

	/**
	 * 报警类型名称
	 */
	@ApiModelProperty(value = "报警类型名称")
	private String alarmTypeName;

	/**
	 * 报警门限id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警门限id")
	private Long thresholdId;

	/**
	 * 是否可选
	 */
	@ApiModelProperty(value = "是否可选")
	private Boolean canSelect;

	public ThresholdSelectVO(){

	}

	/**
	 * 构造方法
	 *
	 * @param alarmType
	 * @param alarmTypeName
	 * @param canSelect
	 */
	public ThresholdSelectVO(Integer alarmType, String alarmTypeName, Boolean canSelect){
		this.setAlarmType(alarmType);
		this.setAlarmTypeName(alarmTypeName);
		this.setCanSelect(canSelect);
	}

}
