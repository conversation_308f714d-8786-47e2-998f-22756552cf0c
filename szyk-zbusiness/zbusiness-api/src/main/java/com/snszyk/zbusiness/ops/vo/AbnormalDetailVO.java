/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.ops.entity.AbnormalDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备异常详情表视图实体类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AbnormalDetailVO对象", description = "设备异常详情表")
public class AbnormalDetailVO extends AbnormalDetail {
	private static final long serialVersionUID = 1L;

	/**
	 * 异常类型（字典：alarm_biz_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常类型（字典：alarm_biz_type）")
	private Integer abnormalType;

	/**
	 * 策略类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "策略类型")
	private Integer strategyType;

	/**
	 * 异常明细列表
	 */
	@ApiModelProperty(value = "异常明细列表")
	private List<AbnormalRecordVO> abnormalRecordList;

	public AbnormalDetailVO(){
		super();
	}

	public AbnormalDetailVO(Integer abnormalType, Integer strategyType){
		super();
		this.abnormalType = abnormalType;
		this.strategyType = strategyType;
	}

}
