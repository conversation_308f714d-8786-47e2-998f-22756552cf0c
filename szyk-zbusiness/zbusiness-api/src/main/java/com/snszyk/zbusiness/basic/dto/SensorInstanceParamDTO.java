/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 传感器实例参数表数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorInstanceParamDTO对象", description = "传感器实例参数表")
public class SensorInstanceParamDTO extends SensorInstanceParam {

    /**
     * 振动类型名称
     */
    @ApiModelProperty(value = "振动类型名称")
    private String vibrationTypeName;

    /**
     * 采样数据类型名称
     */
    @ApiModelProperty(value = "采样数据类型名称")
    private String sampleDataTypeName;

    /**
     * 轴方向名称
     */
    @ApiModelProperty(value = "测量方向名称")
    private String axialDirectionName;

    /**
     * 特征值名称
     */
    @ApiModelProperty(value = "特征值名称")
    private String featureName;

    /**
     * 是否可以编辑 - 如果此位号有数据了，则不可编辑！
     */
    @ApiModelProperty(value = "是否可以编辑")
    private Boolean canEdit;

    /**
     * 采集点code
     */
    @ApiModelProperty(value = "采集点code")
    private String iotCode;

    /**
     * 采集配置唯一标识
     */
    @ApiModelProperty(value = "采集配置唯一标识")
    private Long dcId;

}
