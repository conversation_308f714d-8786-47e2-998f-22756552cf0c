package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.enums.ModelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 重点关注设备
 */
@Data
public class ImportantEquipment implements Serializable {

	@ApiModelProperty(value = "设备ID")
	private String id;
	@ApiModelProperty(value = "设备编码")
	private String code;
	@ApiModelProperty(value = "设备名称")
	private String name;
	@ApiModelProperty(value = "异常级别")
	private Integer abnormalLevel;
	@ApiModelProperty(value = "异常级别")
	private String abnormalLevelName;
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	@ApiModelProperty(value = "维修建议")
	private String suggestion;
	@ApiModelProperty(value = "原因")
	private String abnormalReason;

	public static String[] key = new String[]{"正常","轻度","一般","重度","严重"};


	public String getAbnormalReason() {
		String[] str = abnormalReason.split(",");
		StringBuffer data = new StringBuffer();
		for(String s : str){
			data.append(ModelTypeEnum.getByCode(Integer.parseInt(s)).getName());
			data.append(",");

		}
		return data.substring(0,data.length()-1);
	}
}
