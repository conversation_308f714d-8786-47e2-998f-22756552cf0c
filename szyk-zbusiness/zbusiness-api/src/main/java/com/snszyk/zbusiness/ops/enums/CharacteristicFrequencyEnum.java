package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 轴承特征频率枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CharacteristicFrequencyEnum {

	/**
	 * 外圈特征频率
	 */
	BPFO("bpfo", "外圈特征频率"),

	/**
	 * 内圈特征频率
	 */
	BPFI("bpfi", "内圈特征频率"),

	/**
	 * 保持架特征频率
	 */
	FTF("ftf", "保持架特征频率"),

	/**
	 * 滚动体特征频率
	 */
	BSF("bsf", "滚动体特征频率");


	final String code;
	final String name;

	public static CharacteristicFrequencyEnum getByCode(String code) {
		for (CharacteristicFrequencyEnum value : CharacteristicFrequencyEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return null;
	}

}
