/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 报警周期配置 视图实体类
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AlarmPeriodConfigVO对象", description = "报警周期配置")
public class AlarmPeriodConfigVO implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 报警周期
	 */
	@ApiModelProperty(value = "报警周期")
	private BigDecimal period;

}
