/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 轴承库表导出excel
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@ColumnWidth(25)
public class BearingExcelDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 制造厂商
	 */
	@ExcelProperty(value = {"制造厂商"}, index = 0)
	@ApiModelProperty(value = "制造厂商")
	private String manufacturer;
	/**
	 * 型号
	 */
	@ExcelProperty(value = {"型号"}, index = 1)
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	 * 滚子/滚柱数目
	 */
	@ExcelProperty(value = {"滚子/滚柱数目"}, index = 2)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "滚子/滚柱数目")
	private Integer rollerNumber;
	/**
	 * BPFO
	 */
	@ExcelProperty(value = {"BPFO"}, index = 3)
	@ApiModelProperty(value = "BPFO")
	private BigDecimal bpfo;
	/**
	 * BPFI
	 */
	@ExcelProperty(value = {"BPFI"}, index = 4)
	@ApiModelProperty(value = "BPFI")
	private BigDecimal bpfi;
	/**
	 * FTF
	 */
	@ExcelProperty(value = {"FTF"}, index = 5)
	@ApiModelProperty(value = "FTF")
	private BigDecimal ftf;
	/**
	 * BSF
	 */
	@ExcelProperty(value = {"BSF"}, index = 6)
	@ApiModelProperty(value = "BSF")
	private BigDecimal bsf;

}
