package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @ClassName: DeviceFilterVO
 * @author: wangmh
 * @create: 2022-12-21 13:43
 **/
@Data
@ApiModel(value = "DeviceFilterVO对象", description = "DeviceFilterVO对象")
public class DeviceFilterVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "产线id")
	private Long deviceId;

	@ApiModelProperty(value = "设备名称")
	private String name;

	@ApiModelProperty(value = "报警等级")
	private Integer alarmLevel;

	@ApiModelProperty(value = "设备分类，多个用','逗号分隔")
	private String category;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "生产工艺")
	private Integer produceTech;

}
