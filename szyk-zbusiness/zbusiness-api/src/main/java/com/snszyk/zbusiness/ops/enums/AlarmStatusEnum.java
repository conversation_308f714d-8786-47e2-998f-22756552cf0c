/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警管理业务单据状态枚举类
 *
 * <AUTHOR>
 * @date 2022/12/13 15:10
 **/
@Getter
@AllArgsConstructor
public enum AlarmStatusEnum {

	/**
	 * 待处理
	 */
	WAIT_HANDLE(0, "待处理"),
	/**
	 * 已成故障
	 */
	IS_FAULT(1, "已成故障"),
	/**
	 * 已关闭
	 */
	CLOSED(2, "已关闭"),
	;

	final Integer code;
	final String name;

	public static AlarmStatusEnum getByCode(Integer code){
		for (AlarmStatusEnum value : AlarmStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
