/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警门限-指标枚举类
 *
 * <AUTHOR>
 * @date 2023/03/15 09:56
 **/
@Getter
@AllArgsConstructor
public enum ThresholdQuotaTypeEnum {

	/**
	 * 有效值
	 */
	EFFECTIVE_VALUE("MM000", "有效值"),
	/**
	 * 峰值
	 */
	PEAK_VALUE("MMN01", "峰值"),
	/**
	 * 峰峰值
	 */
	PEAK_PEAK_VALUE("MMN02", "峰峰值"),
	/**
	 * 裕度
	 */
	MARGIN_VALUE("MMN03", "裕度"),
	/**
	 * 歪度
	 */
	SKEWNESS_VALUE("MMN04", "歪度"),
	/**
	 * 峭度
	 */
	KURTOSIS_VALUE("MMN05", "峭度"),
	/**
	 * 设备温度
	 */
	DEVICE_TEMP("DTEMP", "设备温度"),
	/**
	 * 环境温度
	 */
	ENVIRONMENT_TEMP("ETEMP", "环境温度"),
	/**
	 * 传感器电量
	 */
	SENSOR_POWER("SPOWER", "传感器电量"),
	/**
	 * 传感器在线状态
	 */
	SENSOR_ONLINE("SONLINE", "传感器在线状态"),
	/**
	 * 设备运行状态
	 */
	EQUIPMENT_RUNNING("ERUNNING", "设备运行状态"),
	/**
	 * 电量
	 */
	BATTERY_LEVEL("SPOWER", "传感器电量"),
	/**
	 * 电量
	 */
	VOLTAGE("VOLTAGE", "电压"),
	/**
	 * 电流
	 */
	ELECTRIC("ELECTRIC", "电流"),
	/**
	 * 转速
	 */
	RPM("RPM", "转速"),
	;

	final String code;
	final String name;

	public static ThresholdQuotaTypeEnum getByCode(String code){
		for (ThresholdQuotaTypeEnum value : ThresholdQuotaTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
