/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.basic.dto.MechanismModelDTO;
import com.snszyk.zbusiness.basic.dto.SystemConfigStatDTO;
import com.snszyk.zbusiness.basic.entity.MechanismModel;
import com.snszyk.zbusiness.basic.vo.MechanismModelVO;

import java.util.List;

/**
 * 机理模型 服务类
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
public interface IMechanismModelService extends BaseService<MechanismModel> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mechanismModel
	 * @return
	 */
	IPage<MechanismModelDTO> page(IPage<MechanismModelDTO> page, MechanismModelVO mechanismModel);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	MechanismModelDTO detail(Long id);

	/**
	 * 提交
	 *
	 * @param mechanismModel
	 * @return
	 */
	boolean submit(MechanismModelVO mechanismModel);

	/**
	 * 应用
	 *
	 * @param id
	 * @param monitorIds
	 * @param applyStatus
	 * @return
	 */
	Integer distribute(Long id, List<Long> monitorIds, Integer applyStatus);

	/**
	 * 门户端-系统配置概览
	 *
	 * @return
	 */
	SystemConfigStatDTO systemConfigStat();

	/**
	 * 配置部位上的机理模型
	 *
	 * @param mechanismModel
	 * @return
	 */
	boolean configMonitorModel(MechanismModelVO mechanismModel);

	/**
	 * 部位上的机理模型详情
	 *
	 * @param monitorId
	 * @param modelId
	 * @return
	 */
	MechanismModelDTO monitorModelDetail(Long monitorId, Long modelId);

}
