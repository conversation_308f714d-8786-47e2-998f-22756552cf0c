/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警关闭原因枚举类
 *
 * <AUTHOR>
 * @date 2022/12/16 15:10
 **/
@Getter
@AllArgsConstructor
public enum AlarmCloseReasonEnum {

	/**
	 * 安装问题导致
	 */
	INSTALL_PROBLEM(0, "安装问题导致"),
	/**
	 * 运行维护导致
	 */
	OPERATE_MAINTAIN(1, "运行维护导致"),
	/**
	 * 现场维护处理
	 */
	ONSITE_MAINTAIN(2, "现场维护处理"),
	/**
	 * 现场维修处理
	 */
	ONSITE_REPAIR(3, "现场维修处理"),
	/**
	 * 无异常
	 */
	NO_EXCEPTION(4, "无异常"),
	/**
	 * 其他
	 */
	OTHER_REASON(5, "其他"),
	/**
	 * 故障闭环
	 */
	CLOSED_LOOP(6, "故障闭环"),
	/**
	 * 自动关闭
	 */
	AUTO_CLOSED(7, "自动关闭"),
	;

	final Integer code;
	final String name;

	public static AlarmCloseReasonEnum getByCode(Integer code){
		for (AlarmCloseReasonEnum value : AlarmCloseReasonEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
