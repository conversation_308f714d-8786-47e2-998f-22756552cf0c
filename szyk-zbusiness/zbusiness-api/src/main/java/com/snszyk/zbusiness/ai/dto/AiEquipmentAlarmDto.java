package com.snszyk.zbusiness.ai.dto;

import lombok.Data;

@Data
public class AiEquipmentAlarmDto {

	private Long id;

	private Long alarmId;

	private Long equipmentId;

	private String equipmentName;

	private Integer diagnosisType;

	private Integer alarmLevel;

	private String conclusion;

	private String suggestion;

	private Long diagnoseUser;

	public String convertLLmPrompt() {
		StringBuilder b = new StringBuilder();
		b.append("设备名称：")
		 .append(this.equipmentName)
		 .append("，报警信息：")
		 .append(this.conclusion);
		return b.toString();
	}
}
