package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器数据 - 对应 sidas_sensor_data_{monitorId} 表，通过MonitorTableNameHandler实现动态匹配。
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("sidas_sensor_data")
@ApiModel(value = "SensorData对象", description = "传感器数据表")
public class SensorData implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 部位ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("部位ID")
	private Long monitorId;
	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形id")
	private Long waveId;
	/**
	 * 传感器实例编码
	 */
	@ApiModelProperty(value = "传感器实例编码")
	private String sensorCode;
	/**
	 * 传感器实例
	 */
	@ApiModelProperty("传感器实例")
	private String sensorInstance;
	/**
	 * 采样数据类型
	 */
	@ApiModelProperty(value = "采样数据类型")
	private String sampleDataType;
	/**
	 * 轴方向
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "轴方向")
	private Integer axisDirection;
	/**
	 * 测量方向
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测量方向")
	private Integer measureDirection;
	/**
	 * 采样频率（KHz）
	 */
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;
	/**
	 * 采样点数
	 */
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;
	/**
	 * 时域波形 - 振动类型
	 */
	@ToString.Exclude
	@ApiModelProperty("时域波形 - 振动类型")
	private String timeDomainWaveform;
	/**
	 * 采集值 - 非振动数据
	 */
	@ApiModelProperty("采集值 - 非振动数据")
	private BigDecimal value;
	/**
	 * 有效值
	 */
	@ApiModelProperty("有效值")
	private BigDecimal rmsValue;
	/**
	 * 峰值
	 */
	@ApiModelProperty("峰值")
	private BigDecimal peakValue;
	/**
	 * 峰峰值
	 */
	@ApiModelProperty("峰峰值")
	private BigDecimal peakPeakValue;
	/**
	 * 裕度
	 */
	@ApiModelProperty("裕度")
	private BigDecimal clearanceValue;
	/**
	 * 歪度
	 */
	@ApiModelProperty("歪度")
	private BigDecimal skewnessValue;
	/**
	 * 峭度
	 */
	@ApiModelProperty("峭度")
	private BigDecimal kurtosisValue;
	/**
	 * 能量值 - 应力波
	 */
	@ApiModelProperty("能量值")
	private BigDecimal energyValue;
	/**
	 * 采集时间
	 */
	@ApiModelProperty("采集时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date originTime;
	/**
	 * 是否异常数据：0-正常；1-异常
	 */
	@ApiModelProperty(value = "是否异常数据：0-正常；1-异常")
	private Integer invalid;
	/**
	 * 异常原因
	 */
	@ApiModelProperty("异常原因")
	private String invalidReason;
	/**
	 * 是否已标记：0-否，1-是。
	 */
	@ApiModelProperty(value = "是否已标记：0-否，1-是。")
	private Integer isMarked;
	/**
	 * 报警等级
	 */
	@ApiModelProperty(value = "报警等级")
	private Integer alarmLevel;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 波形数据
	 */
	@ApiModelProperty("波形数据")
	private String waveformUrl;
	/**
	 * 是否有波形数据
	 */
	@ApiModelProperty("是否有波形数据（0：无波形数据；1：有波形数据；）")
	private Integer hasWaveData;

}
