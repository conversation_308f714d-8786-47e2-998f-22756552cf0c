package com.snszyk.zbusiness.ops.listener;

import com.snszyk.common.constant.EolmConstant;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceVO;
import com.snszyk.zbusiness.ops.service.logic.AlarmThresholdLogicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报警门限监听Listener
 *
 * <AUTHOR>
 * @date 2023/03/15 09:56
 **/
@Slf4j
@Configuration
public class AlarmThresholdListener {

	@Resource
	private AlarmThresholdLogicService alarmThresholdLogicService;

	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_ALARM_THRESHOLD_ADD)
	public void daqAddAlarmThreshold(List<SensorInstanceVO> dataList){
		log.info("=====》开始初始化通用指标报警门限数据：========{}", dataList);
		alarmThresholdLogicService.initAlarmThreshold(dataList);
		log.info("=====》通用指标报警门限数据初始化结束：========{}", dataList);
	}

	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_ALARM_THRESHOLD_REMOVE)
	public void removeAlarmThreshold(String paths){
		log.info("=====》开始删除报警门限数据：========{}", paths);
		alarmThresholdLogicService.removeAlarmThreshold(paths);
		log.info("=====》报警门限数据删除结束：========{}", paths);
	}

	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_ALARM_THRESHOLD_UPDATE)
	public void updateAlarmThreshold(EquipmentVO vo){
		log.info("=====》开始更新通用指标报警门限功率：========{}", vo);
		alarmThresholdLogicService.updateAlarmThreshold(vo);
		log.info("=====》通用指标报警门限功率更新结束：========{}", vo);
	}

}
