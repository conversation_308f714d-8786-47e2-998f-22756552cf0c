<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.ops.mapper.DiagnosticReportDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="diagnosticReportDetailResultMap" type="com.snszyk.zbusiness.ops.entity.DiagnosticReportDetail">
        <result column="id" property="id"/>
        <result column="report_id" property="reportId"/>
        <result column="alarm_time" property="alarmTime"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_index" property="alarmIndex"/>
        <result column="sort" property="sort"/>
    </resultMap>


    <delete id="removeByReportId">
        delete from eolm_diagnostic_report_detail where report_id = #{reportId}
    </delete>

</mapper>
