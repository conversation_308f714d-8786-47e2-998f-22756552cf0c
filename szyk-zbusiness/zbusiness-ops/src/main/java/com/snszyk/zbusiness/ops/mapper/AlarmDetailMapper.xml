<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.ops.mapper.AlarmDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="alarmDetailResultMap" type="com.snszyk.zbusiness.ops.entity.AlarmDetail">
        <result column="id" property="id"/>
        <result column="alarm_id" property="alarmId"/>
        <result column="wave_id" property="waveId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="monitor_path" property="monitorPath"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_index" property="alarmIndex"/>
        <result column="val" property="val"/>
        <result column="unit" property="unit"/>
        <result column="first_alarm_time" property="firstAlarmTime"/>
        <result column="last_alarm_time" property="lastAlarmTime"/>
        <result column="alarm_data_time" property="alarmDataTime"/>
        <result column="conclusion" property="conclusion"/>
        <result column="times" property="times"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="alarmDetailDTOResultMap" type="com.snszyk.zbusiness.ops.dto.AlarmDetailDTO">
        <result column="id" property="id"/>
        <result column="alarm_id" property="alarmId"/>
        <result column="wave_id" property="waveId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="monitor_path" property="monitorPath"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_index" property="alarmIndex"/>
        <result column="val" property="val"/>
        <result column="unit" property="unit"/>
    </resultMap>


    <select id="queryAlarmDetail" resultMap="alarmDetailDTOResultMap">
        SELECT d.* FROM eolm_alarm_detail d LEFT JOIN eolm_alarm a ON a.id = d.alarm_id
        WHERE a.`status` != 2 AND d.monitor_id = #{monitorId} AND d.alarm_index = #{alarmIndex}
        <if test="waveId!=null">
            AND d.wave_id = #{waveId}
        </if>
    </select>


</mapper>
