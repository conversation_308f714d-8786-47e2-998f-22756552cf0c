/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.ops.entity.AlarmChart;
import com.snszyk.zbusiness.ops.mapper.AlarmChartMapper;
import com.snszyk.zbusiness.ops.service.IAlarmChartService;
import com.snszyk.zbusiness.ops.vo.AlarmChartVO;
import com.snszyk.zbusiness.ops.wrapper.AlarmChartWrapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报警管理图谱表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Service
public class AlarmChartServiceImpl extends ServiceImpl<AlarmChartMapper, AlarmChart> implements IAlarmChartService {


	@Override
	public List<AlarmChartVO> chartByAlarmRecord(String recordIds) {
		List<AlarmChart> list = this.list(Wrappers.<AlarmChart>query().lambda().in(AlarmChart::getAlarmRecordId, Func.toLongList(recordIds))
			.orderByAsc(AlarmChart::getAlarmTime, AlarmChart::getAlarmIndex));
		if(Func.isNotEmpty(list)){
			return AlarmChartWrapper.build().listVO(list);
		}
		return null;
	}

}
