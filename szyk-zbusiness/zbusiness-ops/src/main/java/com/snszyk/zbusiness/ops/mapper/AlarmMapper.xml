<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.ops.mapper.AlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="alarmResultMap" type="com.snszyk.zbusiness.ops.entity.Alarm">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="tag_no" property="tagNo"/>
        <result column="device_path" property="devicePath"/>
        <result column="path_name" property="pathName"/>
        <result column="vibrate_subarea" property="vibrateSubarea"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_abstract" property="alarmAbstract"/>
        <result column="first_alarm_time" property="firstAlarmTime"/>
        <result column="last_alarm_time" property="lastAlarmTime"/>
        <result column="close_reason" property="closeReason"/>
        <result column="receiver" property="receiver"/>
        <result column="is_fault" property="isFault"/>
        <result column="fault_id" property="faultId"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="alarmDTOResultMap" type="com.snszyk.zbusiness.ops.dto.AlarmDTO">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="tag_no" property="tagNo"/>
        <result column="device_path" property="devicePath"/>
        <result column="path_name" property="pathName"/>
        <result column="vibrate_subarea" property="vibrateSubarea"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="alarm_abstract" property="alarmAbstract"/>
        <result column="first_alarm_time" property="firstAlarmTime"/>
        <result column="last_alarm_time" property="lastAlarmTime"/>
        <result column="is_fault" property="isFault"/>
        <result column="fault_id" property="faultId"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="alarmStatisticDTOResultMap" type="com.snszyk.zbusiness.ops.dto.AlarmStatisticsDTO">
        <result column="region_id" property="regionId"/>
        <result column="region_name" property="orgName"/>
        <result column="region_full_name" property="orgFullName"/>
        <result column="region_path" property="orgPath"/>
        <result column="device_count" property="deviceCount"/>
        <result column="alarm_count" property="alarmCount"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <select id="page" resultMap="alarmDTOResultMap">
        select * from eolm_alarm where is_deleted = 0
        <if test="alarm.tenantId!=null and alarm.tenantId!=''">
            and tenant_id = #{alarm.tenantId}
        </if>
        <if test="alarm.status!=null">
            and status= #{alarm.status}
        </if>
        <if test="alarm.equipmentId!=null">
            and equipment_id = #{alarm.equipmentId}
        </if>
        <if test="alarm.alarmLevel!=null">
            and alarm_level = #{alarm.alarmLevel}
        </if>
        <if test="alarm.startDate!=null">
            and create_time <![CDATA[ >= ]]> #{alarm.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="alarm.endDate!=null">
            and create_time <![CDATA[ <= ]]> #{alarm.endDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>

    <select id="listExport" resultMap="alarmDTOResultMap">
        select * from eolm_alarm where is_deleted = 0
        <if test="alarm.vibrateSubarea!=null">
            and vibrate_subarea= #{alarm.vibrateSubarea}
        </if>
        <if test="alarm.status!=null">
            and status= #{alarm.status}
        </if>
        <if test="alarm.equipmentId!=null">
            and equipment_id=#{alarm.equipmentId}
        </if>
        <if test="alarm.alarmLevel!=null">
            and alarm_level = #{alarm.alarmLevel}
        </if>
        <if test="alarm.startDate!=null">
            and create_time <![CDATA[ >= ]]> #{alarm.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="alarm.endDate!=null">
            and create_time <![CDATA[ <= ]]> #{alarm.endDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>

    <select id="getAlarmStatisticData" resultMap="alarmStatisticDTOResultMap">
        SELECT
            s.*,
            count( DISTINCT a.id ) as alarm_count
        FROM
            (
            SELECT
                t.*,
                count( DISTINCT e.id ) AS device_count
            FROM
                eolm_equipment e
            RIGHT JOIN (
                SELECT
                d.id AS region_id,
                d.path AS region_path,
                d.`name` AS region_name,
                d.path_name AS region_full_name,
                bt.node_level AS region_level,
                ( SELECT CASE WHEN count( 1 ) > 0 THEN 1 ELSE 0 END FROM eolm_device WHERE parent_id = d.id AND is_deleted = 0 AND (`type` = 1 or `type` = 3)) AS "has_children"
                FROM eolm_device d
                LEFT JOIN sidas_basic_tree bt ON bt.id = d.id
                WHERE
                d.is_deleted = 0 AND (d.`type` = 1 or d.`type` = 3)
                AND d.tenant_id = #{alarmStatistic.tenantId}
                AND bt.node_level = #{alarmStatistic.regionLevel}
                <if test="alarmStatistic.parentId!=null">
                    AND d.parent_id = #{alarmStatistic.parentId}
                </if>
            ) t ON e.path LIKE concat( concat( '%', t.region_id ), '%' )
            <where>
                <if test="alarmStatistic.regionId!=null">
                    and t.region_id = #{alarmStatistic.regionId}
                </if>
            </where>
            GROUP BY t.region_id
        ) s
        LEFT JOIN eolm_alarm a ON a.device_path LIKE concat( concat( '%', s.region_id ), '%' )
        <where>
            <if test="alarmStatistic.startDate!=null">
                and a.create_time <![CDATA[ >= ]]> #{alarmStatistic.startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="alarmStatistic.endDate!=null">
                and a.create_time <![CDATA[ <= ]]> #{alarmStatistic.endDate, jdbcType=TIMESTAMP}
            </if>
        </where>
        GROUP BY s.region_id
    </select>

    <select id="getAlarmStatisticExport" resultMap="alarmStatisticDTOResultMap">
        SELECT
            t.*,
            count( DISTINCT e.id ) AS device_count,
            count( DISTINCT a.id ) AS alarm_count
        FROM
            eolm_equipment e
        RIGHT JOIN (
            SELECT
                d.id AS region_id,
                d.path AS region_path,
                d.`name` AS region_name,
                d.path_name AS region_full_name
            FROM eolm_device d
            LEFT JOIN sidas_basic_tree bt ON bt.id = d.id
            WHERE  d.is_deleted = 0 AND (d.`type` = 1 or d.`type` = 3) AND bt.node_level != 1 AND d.tenant_id = #{alarmStatistic.tenantId}
        ) t ON e.path LIKE concat( concat( '%', t.region_id ), '%' )
        LEFT JOIN eolm_alarm a ON a.device_path LIKE concat( concat( '%', t.region_id ), '%' )
        <where>
            <if test="alarmStatistic.regionId!=null">
                and t.region_id = #{alarmStatistic.regionId}
            </if>
            <if test="alarmStatistic.startDate!=null">
                and a.create_time <![CDATA[ >= ]]> #{alarmStatistic.startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="alarmStatistic.endDate!=null">
                and a.create_time <![CDATA[ <= ]]> #{alarmStatistic.endDate, jdbcType=TIMESTAMP}
            </if>
        </where>
        GROUP BY t.region_id
        ORDER BY t.region_path
    </select>

    <select id="getLast2MonthsAlarmCount" resultType="java.util.Map">
        SELECT DATE_FORMAT(alarm_time, '%Y-%m') AS `month`, COUNT(*) AS `count`
        FROM eolm_alarm_record
        where alarm_time >= CURDATE() - INTERVAL 2 MONTH
        GROUP BY `month`
        ORDER BY `month` DESC LIMIT 2
    </select>

</mapper>
