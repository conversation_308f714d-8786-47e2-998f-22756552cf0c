package com.snszyk.zbusiness.ops.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.BasicTreeNameUpdateDTO;
import com.snszyk.zbusiness.ops.entity.Alarm;
import com.snszyk.zbusiness.ops.entity.AlarmDetail;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import com.snszyk.zbusiness.ops.entity.DiagnosisRecord;
import com.snszyk.zbusiness.ops.service.IAlarmDetailService;
import com.snszyk.zbusiness.ops.service.IAlarmRecordService;
import com.snszyk.zbusiness.ops.service.IAlarmService;
import com.snszyk.zbusiness.ops.service.IDiagnosisRecordService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 生产结构树节点名称（地点、设备、测点）更新监听 - diagnosis服务
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class BasicTreeNameUpdateDiagnosisListener {

	private final IAlarmService alarmService;
	private final IAlarmDetailService alarmDetailService;
	private final IAlarmRecordService alarmRecordService;
	private final IDiagnosisRecordService diagnosisRecordService;

	/**
	 * 涉及的表 & 字段
	 *  eolm_alarm：equipment_id、equipment_name、path_name（设备路径）
	 * 	eolm_alarm_detail：monitor_id、monitor_name
	 * 	eolm_alarm_record：monitor_id、monitor_name
	 * 	eolm_diagnosis_record：equipment_id、equipment_name
	 *
	 */
	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_BASIC_TREE_NAME_UPDATE_DIAGNOSIS)
	public void basicTreeNameUpdate(BasicTreeNameUpdateDTO dto) {
		log.info("生产结构树节点名称更新 - {}", dto);

		if (BasicTreeNameUpdateDTO.TYPE_DEVICE.equals(dto.getType())) {
			// 更新有path的表
			updateAlarmEquipmentPathName(dto);
		} else if (BasicTreeNameUpdateDTO.TYPE_EQUIPMENT.equals(dto.getType())) {
			// 更新有equipment_name 和 path的表
			updateAlarmEquipmentNameAndPathName(dto);
			updateDiagnosisRecordEquipmentName(dto);
		} else if (BasicTreeNameUpdateDTO.TYPE_MONITOR.equals(dto.getType())) {
			// 更新有monitor_name 和 monitor_path的表
			updateAlarmDetailMonitorName(dto);
			updateAlarmRecordMonitorName(dto);
		} else {
			log.warn("不支持的type = {}", dto.getType());
		}
	}

	private void updateAlarmEquipmentPathName(BasicTreeNameUpdateDTO deviceDto) {
		List<Alarm> alarmList = alarmService.list(Wrappers.<Alarm>lambdaQuery()
			.like(Alarm::getDevicePath, deviceDto.getId()));
		if (Func.isNotEmpty(alarmList)) {
			alarmList.forEach(alarm -> {
				// 找到路径中的索引
				List<String> pathIdList = Arrays.asList(alarm.getDevicePath().split(StringPool.COMMA));
				int pathIdIndex = pathIdList.indexOf(deviceDto.getId().toString());

				// 更新路径对应索引的Name
				List<String> pathNameList = Arrays.asList(alarm.getPathName().split(StringPool.COMMA));
				pathNameList.set(pathIdIndex, deviceDto.getNewName());

				alarm.setPathName(String.join(StringPool.COMMA, pathNameList));
				boolean update = alarmService.updateById(alarm);
				log.info("updateAlarmEquipmentPathName() - 更新eolm_alarm表的pathName结果 = {}，alarmId = {}, pathName = {}", update, alarm.getId(), alarm.getPathName());
			});
		}
	}

	private void updateAlarmEquipmentNameAndPathName(BasicTreeNameUpdateDTO equipmentDto) {
		List<Alarm> alarmList = alarmService.list(Wrappers.<Alarm>lambdaQuery()
			.like(Alarm::getDevicePath, equipmentDto.getId()));
		if (Func.isNotEmpty(alarmList)) {
			alarmList.forEach(alarm -> {
				// 找到路径中的索引
				List<String> pathIdList = Arrays.asList(alarm.getDevicePath().split(StringPool.COMMA));
				int pathIdIndex = pathIdList.indexOf(equipmentDto.getId().toString());

				// 更新路径对应索引的Name
				List<String> pathNameList = Arrays.asList(alarm.getPathName().split(StringPool.COMMA));
				pathNameList.set(pathIdIndex, equipmentDto.getNewName());

				alarm.setEquipmentName(equipmentDto.getNewName());
				alarm.setPathName(String.join(StringPool.COMMA, pathNameList));
				boolean update = alarmService.updateById(alarm);
				log.info("updateAlarmEquipmentNameAndPathName() - 更新eolm_alarm表的pathName结果 = {}，alarmId = {}, equipmentName = {}, pathName = {}",
					update, alarm.getId(), alarm.getEquipmentName(), alarm.getPathName());
			});
		}
	}

	private void updateDiagnosisRecordEquipmentName(BasicTreeNameUpdateDTO equipmentDto) {
		List<DiagnosisRecord> diagnosisRecordList = diagnosisRecordService.list(Wrappers.<DiagnosisRecord>lambdaQuery()
			.eq(DiagnosisRecord::getEquipmentId, equipmentDto.getId()));
		if (Func.isNotEmpty(diagnosisRecordList)) {
			diagnosisRecordList.forEach(diagnosisRecord -> {
				diagnosisRecord.setEquipmentName(equipmentDto.getNewName());
				boolean update = diagnosisRecordService.updateById(diagnosisRecord);
				log.info("updateDiagnosisRecordEquipmentName() - 更新eolm_diagnosis_record表的equipmentName结果 = {}，diagnosisRecordId = {}, equipmentName = {}",
					update, diagnosisRecord.getId(), diagnosisRecord.getEquipmentName());
			});
		}
	}

	private void updateAlarmDetailMonitorName(BasicTreeNameUpdateDTO monitorDto) {
		List<AlarmDetail> alarmDetailList = alarmDetailService.list(Wrappers.<AlarmDetail>lambdaQuery()
			.eq(AlarmDetail::getMonitorId, monitorDto.getId()));
		if (Func.isNotEmpty(alarmDetailList)) {
			alarmDetailList.forEach(alarmDetail -> {
				alarmDetail.setMonitorName(monitorDto.getNewName());
				boolean update = alarmDetailService.updateById(alarmDetail);
				log.info("updateAlarmDetailMonitorName() - 更新eolm_alarm_detail表的monitorName结果 = {}，alarmDetailId = {}, monitorName = {}",
					update, alarmDetail.getId(), alarmDetail.getMonitorName());
			});
		}
	}

	private void updateAlarmRecordMonitorName(BasicTreeNameUpdateDTO monitorDto) {
		List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>lambdaQuery()
			.eq(AlarmRecord::getMonitorId, monitorDto.getId()));
		if (Func.isNotEmpty(alarmRecordList)) {
			alarmRecordList.forEach(alarmRecord -> {
				alarmRecord.setMonitorName(monitorDto.getNewName());
				boolean update = alarmRecordService.updateById(alarmRecord);
				log.info("updateAlarmRecordMonitorName() - 更新eolm_alarm_record表的monitorName结果 = {}，alarmRecordId = {}, monitorName = {}",
					update, alarmRecord.getId(), alarmRecord.getMonitorName());
			});
		}
	}

}
