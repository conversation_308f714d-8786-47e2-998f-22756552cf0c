/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import com.snszyk.zbusiness.ops.vo.AbnormalRecordVO;

import java.util.List;

/**
 * 设备异常明细表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public interface AbnormalRecordMapper extends BaseMapper<AbnormalRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param abnormalRecord
	 * @return
	 */
	List<AbnormalRecordVO> selectAbnormalRecordPage(IPage page, AbnormalRecordVO abnormalRecord);

}
