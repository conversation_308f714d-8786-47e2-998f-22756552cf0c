/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.logic;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.zbusiness.ops.dto.AiopsDiagnosisReportDto;
import com.snszyk.zbusiness.ops.dto.CommonDeleteResultDto;
import com.snszyk.zbusiness.ops.entity.AiopsDiagnosisReport;
import com.snszyk.zbusiness.ops.enums.DiagnosisReportTypeEnum;
import com.snszyk.zbusiness.ops.enums.PublishStatusEnum;
import com.snszyk.zbusiness.ops.service.IAiopsDiagnosisReportService;
import com.snszyk.zbusiness.ops.vo.AiopsDiagnosisReportAVo;
import com.snszyk.zbusiness.ops.vo.AiopsDiagnosisReportPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 诊断报告表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@AllArgsConstructor
@Service
public class AiopsDiagnosisReportLogicService {

	private final IAiopsDiagnosisReportService aiopsDiagnosisReportService;
	private final IAttachService attachService;
	private final DiagnosisReportMessageService diagnosisReportMessageService;

	@Transactional
	public AiopsDiagnosisReportDto saveOrUpdate(AiopsDiagnosisReportAVo v) {
		// 名称的唯一性校验
		AiopsDiagnosisReport copy = BeanUtil.copy(v, AiopsDiagnosisReport.class);
		if(v.getId()==null){
			// 新增时，设置状态为未发布
			copy.setStatus(PublishStatusEnum.UNPUBLISHED.getCode());
			//默认是月度诊断报告
			copy.setType(DiagnosisReportTypeEnum.MONTHLY_DIAGNOSIS_REPORT.getCode());
		}
		boolean b = aiopsDiagnosisReportService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<AiopsDiagnosisReportDto> pageList(AiopsDiagnosisReportPageVo v) {
		// 查询分页数据
		IPage<AiopsDiagnosisReportDto> page = aiopsDiagnosisReportService.pageList(v);

		// 获取所有需要查询附件的报告的attachId
		List<Long> attachIds = page.getRecords().stream()
			.filter(dto -> Func.isNotEmpty(dto.getAttachId()))
			.map(AiopsDiagnosisReportDto::getAttachId)
			.distinct()
			.collect(Collectors.toList());

		// 如果有附件ID，则批量查询附件信息
		if (Func.isNotEmpty(attachIds)) {
			// 一次查询所有附件，避免多次查询数据库
			List<Attach> attachList = attachService.listByIds(attachIds);

			// 将附件按ID分组，方便快速查找
			Map<Long, List<Attach>> attachMap = attachList.stream()
				.collect(Collectors.groupingBy(Attach::getId));

			// 为每个报告设置附件列表
			page.getRecords().forEach(dto -> {
				if (Func.isNotEmpty(dto.getAttachId()) && attachMap.containsKey(dto.getAttachId())) {
					dto.setAttachList(attachMap.get(dto.getAttachId()));
				}
			});
		}

		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public AiopsDiagnosisReportDto detail(Long id) {
		// 通过ID查询数据信息
		AiopsDiagnosisReportDto dto = aiopsDiagnosisReportService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}

		// 获取附件信息
		if (Func.isNotEmpty(dto.getAttachId())) {
			List<Attach> attachList = attachService.listByIds(Collections.singletonList(dto.getAttachId()));
			if (ObjectUtil.isNotEmpty(attachList)) {
				dto.setAttachList(attachList);
			}
		}

		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			AiopsDiagnosisReport data = aiopsDiagnosisReportService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}
			deleteResultDto.setName(data.getTitle());
			boolean b = aiopsDiagnosisReportService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}

	/**
	 * 发布报告
	 *
	 * @param id 报告主键
	 * @return 是否发布成功
	 */
	@Transactional
	public boolean publish(Long id) {
		// 检查数据是否存在
		AiopsDiagnosisReport report = aiopsDiagnosisReportService.getById(id);
		if (report == null) {
			throw new ServiceException("数据不存在");
		}

		// 调用服务层方法发布报告
		boolean result = aiopsDiagnosisReportService.publish(id);
		if (!result) {
			throw new ServiceException("发布失败");
		}

		return true;
	}

	/**
	 * 记录客户预览
	 *
	 * @param id 报告主键
	 * @return 是否记录成功
	 */
	@Transactional
	public boolean recordPreview(Long id) {
		// 检查数据是否存在
		AiopsDiagnosisReport report = aiopsDiagnosisReportService.getById(id);
		if (report == null) {
			throw new ServiceException("数据不存在");
		}

		// 调用服务层方法记录客户预览
		boolean result = aiopsDiagnosisReportService.recordPreview(id);
		if (!result) {
			throw new ServiceException("记录预览失败");
		}
		return true;
	}

	/**
	 * 记录客户下载
	 *
	 * @param id 报告主键
	 * @return 是否记录成功
	 */
	@Transactional
	public boolean recordDownload(Long id) {
		// 检查数据是否存在
		AiopsDiagnosisReport report = aiopsDiagnosisReportService.getById(id);
		if (report == null) {
			throw new ServiceException("数据不存在");
		}

		// 调用服务层方法记录客户下载
		boolean result = aiopsDiagnosisReportService.recordDownload(id);
		if (!result) {
			throw new ServiceException("记录下载失败");
		}
		return true;
	}

	/**
	 * 检查是否存在未发布的报告
	 *
	 * @param tenantId 租户ID
	 * @return 是否存在未发布的报告
	 */
	public boolean hasUnpublishedReport(String tenantId) {
		// 调用服务层方法检查是否存在未发布的报告
		return aiopsDiagnosisReportService.hasUnpublishedReport(tenantId);
	}

}
