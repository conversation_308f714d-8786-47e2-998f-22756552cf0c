/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.ai.vo.StandardDictVO;
import com.snszyk.zbusiness.basic.enums.RealNonVibrationDataEnum;
import com.snszyk.zbusiness.ops.dto.AlarmThresholdDTO;
import com.snszyk.zbusiness.ops.entity.AlarmThreshold;
import com.snszyk.zbusiness.ops.mapper.AlarmThresholdMapper;
import com.snszyk.zbusiness.ops.service.IAlarmThresholdService;
import com.snszyk.zbusiness.ops.vo.AlarmThresholdVO;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 报警门限表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Service
public class AlarmThresholdServiceImpl extends BaseServiceImpl<AlarmThresholdMapper, AlarmThreshold> implements IAlarmThresholdService {

	@Override
	public IPage<AlarmThresholdDTO> vibrationQuotaPage(IPage<AlarmThresholdDTO> page, AlarmThresholdVO alarmThreshold) {
		return page.setRecords(baseMapper.vibrationQuotaPage(page, alarmThreshold));
	}

	@Override
	public IPage<AlarmThresholdDTO> stressQuotaPage(IPage<AlarmThresholdDTO> page, AlarmThresholdVO alarmThreshold) {
		return page.setRecords(baseMapper.stressQuotaPage(page, alarmThreshold));
	}

	@Override
	public IPage<AlarmThresholdDTO> nonVibrationQuotaPage(IPage<AlarmThresholdDTO> page, AlarmThresholdVO alarmThreshold) {
		return page.setRecords(baseMapper.nonVibrationQuotaPage(page, alarmThreshold));
	}

	@Override
	public boolean applyStandardThreshold(StandardDictVO standardDict) {
		boolean flag = Arrays.stream(RealNonVibrationDataEnum.values()).anyMatch(t ->
			Func.equals(t.getCode(), standardDict.getSampleDataType()));
		if(flag){
			List<AlarmThreshold> list = this.list(Wrappers.<AlarmThreshold>query().lambda()
				.eq(AlarmThreshold::getSampleDataType, standardDict.getSampleDataType())
				.eq(AlarmThreshold::getAlarmType, standardDict.getAlarmType()).in(AlarmThreshold::getMonitorId, standardDict.getMonitorIds()));
			if(Func.isNotEmpty(list)){
				list.forEach(alarmThreshold -> {
					alarmThreshold.setFirstThresholdLower(standardDict.getFirstThresholdLower())
						.setFirstThresholdUpper(standardDict.getFirstThresholdUpper())
						.setSecondThresholdLower(standardDict.getSecondThresholdLower())
						.setSecondThresholdUpper(standardDict.getSecondThresholdUpper())
						.setThirdThresholdLower(standardDict.getThirdThresholdLower())
						.setThirdThresholdUpper(standardDict.getThirdThresholdUpper())
						.setFourthThresholdLower(standardDict.getFourthThresholdLower())
						.setFourthThresholdUpper(standardDict.getFourthThresholdUpper());
				});
				return this.updateBatchById(list);
			}
			return Boolean.FALSE;
		}
		List<AlarmThreshold> list = this.list(Wrappers.<AlarmThreshold>lambdaQuery()
			.eq(AlarmThreshold::getQuotaType, standardDict.getQuotaType()).eq(AlarmThreshold::getAlarmType, standardDict.getAlarmType())
			.in(AlarmThreshold::getWaveId, standardDict.getWaveIds()));
		if(Func.isNotEmpty(list)){
			list.forEach(alarmThreshold -> {
				alarmThreshold.setFirstThresholdLower(standardDict.getFirstThresholdLower())
					.setFirstThresholdUpper(standardDict.getFirstThresholdUpper())
					.setSecondThresholdLower(standardDict.getSecondThresholdLower())
					.setSecondThresholdUpper(standardDict.getSecondThresholdUpper())
					.setThirdThresholdLower(standardDict.getThirdThresholdLower())
					.setThirdThresholdUpper(standardDict.getThirdThresholdUpper())
					.setFourthThresholdLower(standardDict.getFourthThresholdLower())
					.setFourthThresholdUpper(standardDict.getFourthThresholdUpper());
			});
            return this.saveOrUpdateBatch(list);
		}
		return Boolean.FALSE;
	}

}
