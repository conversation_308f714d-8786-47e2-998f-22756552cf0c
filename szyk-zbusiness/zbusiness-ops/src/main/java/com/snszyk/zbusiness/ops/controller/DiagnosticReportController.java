/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO;
import com.snszyk.zbusiness.ops.entity.DiagnosticReport;
import com.snszyk.zbusiness.ops.service.IDiagnosticReportService;
import com.snszyk.zbusiness.ops.service.logic.DiagnosticReportLogicService;
import com.snszyk.zbusiness.ops.vo.DiagnosticReportVO;
import com.snszyk.zbusiness.ops.wrapper.DiagnosticReportWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 诊断报告表 控制器
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/diagnosticReport")
@Api(value = "诊断报告表new", tags = "诊断报告表接口")
public class DiagnosticReportController extends SzykController {

	private final IDiagnosticReportService diagnosticReportService;
	private final DiagnosticReportLogicService diagnosticReportLogicService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<DiagnosticReportDTO> detail(Long id) {
		return R.data(diagnosticReportLogicService.detail(id));
	}

	/**
	 * 列表 诊断报告表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入diagnosticReport")
	public R<IPage<DiagnosticReportVO>> list(DiagnosticReport diagnosticReport, Query query) {
		IPage<DiagnosticReport> pages = diagnosticReportService.page(Condition.getPage(query), Condition.getQueryWrapper(diagnosticReport));
		return R.data(DiagnosticReportWrapper.build().pageVO(pages));
	}

	/**
	 * 分页 诊断报告表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "name", value = "报告名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "reportNo", value = "报告编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入diagnosticReport")
	public R<IPage<DiagnosticReportDTO>> page(@ApiIgnore DiagnosticReportVO diagnosticReport, Query query) {
		IPage<DiagnosticReportDTO> pages = diagnosticReportLogicService.page(Condition.getPage(query), diagnosticReport);
		return R.data(pages);
	}

	/**
	 * 新增 诊断报告表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入diagnosticReport")
	public R save(@Valid @RequestBody DiagnosticReport diagnosticReport) {
		return R.status(diagnosticReportService.save(diagnosticReport));
	}

	/**
	 * 修改 诊断报告表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入diagnosticReport")
	public R update(@Valid @RequestBody DiagnosticReport diagnosticReport) {
		return R.status(diagnosticReportService.updateById(diagnosticReport));
	}

	/**
	 * 新增或修改 诊断报告表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入diagnosticReport")
	public R<Long> submit(@Valid @RequestBody DiagnosticReportVO diagnosticReport) {
		return R.data(diagnosticReportLogicService.submit(diagnosticReport));
	}

	/**
	 * 删除 诊断报告表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(diagnosticReportService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 导出 诊断报告表
	 */
	@GetMapping("/exportWord/{id}")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "导出", notes = "传入id")
	public void exportWord(@PathVariable(name = "id") Long id, HttpServletResponse response) {
		diagnosticReportLogicService.exportWord(id, response);
		//return R.success("操作成功");
	}

}
