package com.snszyk.zbusiness.ai.factory;

import com.snszyk.zbusiness.ai.enums.DifyMessageType;
import com.snszyk.zbusiness.ai.model.BaseRequest;
import com.snszyk.zbusiness.ai.model.ChatAssistantRequest;
import com.snszyk.zbusiness.ai.model.ChatflowRequest;
import com.snszyk.zbusiness.ai.model.WorkflowRequest;
import org.springframework.stereotype.Component;

@Component
public class RequestFactory {

	/**
	 * 创建请求对象
	 * <p>
	 * 根据消息类型创建对应的请求对象
	 *
	 * @param <T> 返回的请求类型，必须是BaseRequest的子类
	 * @param type 消息类型
	 * @return 创建的请求对象
	 */
	@SuppressWarnings("unchecked")
	public <T extends BaseRequest> T createRequest(DifyMessageType type) {
		switch (type) {
			case CHAT_ASSISTANT:
				return (T) new ChatAssistantRequest();
			case CHATFLOW:
				return (T) new ChatflowRequest();
			case WORKFLOW:
				return (T) new WorkflowRequest();
			default:
				throw new IllegalArgumentException("Unsupported DifyMessageType: " + type);
		}
	}
}
