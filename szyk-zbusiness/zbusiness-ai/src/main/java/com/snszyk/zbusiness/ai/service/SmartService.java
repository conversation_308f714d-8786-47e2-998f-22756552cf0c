package com.snszyk.zbusiness.ai.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.zbusiness.ai.config.DifyProperties;
import com.snszyk.zbusiness.ai.constent.DifyConstent;
import com.snszyk.zbusiness.ai.dto.DifyFileUploadResponseDto;
import com.snszyk.zbusiness.ai.enums.DifyMessageType;
import com.snszyk.zbusiness.ai.factory.RequestFactory;
import com.snszyk.zbusiness.ai.model.BaseRequest;
import com.snszyk.zbusiness.ai.model.ChatAssistantRequest;
import com.snszyk.zbusiness.ai.model.ChatflowRequest;
import com.snszyk.zbusiness.ai.model.WorkflowRequest;
import com.snszyk.zbusiness.ai.util.SseEmitterUtil;
import com.snszyk.zbusiness.ai.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmartService {
    private final RequestFactory requestFactory;
    private final DifyProperties difyProperties;
    private static final ConcurrentHashMap<String, SseEmitter> EMITTER_MAP = new ConcurrentHashMap<>();

	/**
	 * 处理智能对话请求
	 * <p>
	 * 根据消息类型创建对应的请求对象，设置请求参数，并创建SSE连接
	 *
	 * @param v 消息请求参数
	 * @return SSE发射器实例
	 */
	public SseEmitter processRequest(MessageVo v) {
		log.info("dify请求接口：{}", JSON.toJSONString(difyProperties));
		DifyMessageType type = DifyMessageType.getByCode(v.getType());

		// 根据类型创建对应的请求对象
		switch (type) {
			case CHAT_ASSISTANT:
				return processChatAssistantRequest(v, type);
			case CHATFLOW:
				return processChatflowRequest(v, type);
			case WORKFLOW:
				return processWorkflowRequest(v, type);
			default:
				log.warn("Unsupported message type: {}", type);
				throw new IllegalArgumentException("Unsupported message type: " + type);
		}
	}

	/**
	 * 处理聊天助手请求
	 *
	 * @param v 消息请求参数
	 * @param type 消息类型
	 * @return SSE发射器实例
	 */
	private SseEmitter processChatAssistantRequest(MessageVo v, DifyMessageType type) {
		ChatAssistantRequest request = requestFactory.createRequest(type);

		// 设置通用属性
		request.setUser(v.getUser())
			.setBaseUrl(difyProperties.getBaseUrl())
			.setInputs(v.getInputs())
			.setAuthorization(difyProperties.getApiKey().get(v.getType()))
			.setUri(DifyConstent.CHAT_ASSISTANT_PATH);

		// 设置特定属性
		request.setQuery(v.getContent())
			.setConversationId(v.getConversationId());

		// 发送请求并处理响应
		return SseEmitterUtil.createEmitter(request);
	}

	/**
	 * 处理聊天流请求
	 *
	 * @param v 消息请求参数
	 * @param type 消息类型
	 * @return SSE发射器实例
	 */
	private SseEmitter processChatflowRequest(MessageVo v, DifyMessageType type) {
		ChatflowRequest request = requestFactory.createRequest(type);

		// 设置通用属性
		request.setUser(v.getUser())
			.setBaseUrl(difyProperties.getBaseUrl())
			.setInputs(v.getInputs())
			.setAuthorization(difyProperties.getApiKey().get(v.getType()))
			.setUri(DifyConstent.CHATFLOW_PATH);

		// 设置特定属性
		request.setQuery(v.getContent())
			.setConversationId(v.getConversationId());

		// 发送请求并处理响应
		return SseEmitterUtil.createEmitter(request);
	}

	/**
	 * 处理工作流请求
	 *
	 * @param v 消息请求参数
	 * @param type 消息类型
	 * @return SSE发射器实例
	 */
	private SseEmitter processWorkflowRequest(MessageVo v, DifyMessageType type) {
		WorkflowRequest request = requestFactory.createRequest(type);

		// 设置通用属性
		request.setUser(v.getUser())
			.setBaseUrl(difyProperties.getBaseUrl())
			.setInputs(v.getInputs())
			.setAuthorization(difyProperties.getApiKey().get(v.getType()))
			.setUri(DifyConstent.WORKFLOW_PATH);

		// 设置特定属性
		if (Func.isNotBlank(v.getContent())) {
			request.getInputs().put("query", v.getContent());
		}

		// 发送请求并处理响应
		return SseEmitterUtil.createEmitter(request);
	}

    /**
     * 上传文件到Dify平台
     *
     * @param file 文件
     * @param user 用户标识
     * @return Dify文件上传响应信息
     */
    public DifyFileUploadResponseDto uploadFileToDify(MultipartFile file, String user, String type) {
        log.info("开始上传文件到Dify平台，用户标识: {}", user);

        if (file.isEmpty()) {
            throw new ServiceException("上传的文件不能为空");
        }

        if (StringUtil.isBlank(user)) {
            throw new ServiceException("用户标识不能为空");
        }

        try {
            // 创建RestTemplate实例
            RestTemplate restTemplate = new RestTemplate();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Authorization", "Bearer " + difyProperties.getApiKey().get(type));

            // 创建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new HttpEntity<>(file.getResource()));
            body.add("user", user);

            // 创建请求实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 构建Dify文件上传URL
            String uploadUrl = difyProperties.getBaseUrl() + DifyConstent.FILE_UPLOAD_PATH;

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                uploadUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            // 处理响应
            String responseBody = response.getBody();
            log.info("Dify文件上传响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);

            try {
                // 解析响应JSON
                JSONObject jsonObject = JSON.parseObject(responseBody);

                // 检查是否包含id字段，如果有则说明上传成功
                if (jsonObject.containsKey("id")) {
                    log.info("Dify文件上传成功");

                    DifyFileUploadResponseDto responseDto = new DifyFileUploadResponseDto();
                    responseDto.setId(jsonObject.getString("id"));
                    responseDto.setName(jsonObject.getString("name"));
                    responseDto.setSize(jsonObject.getLong("size"));
                    responseDto.setExtension(jsonObject.getString("extension"));
                    responseDto.setMimeType(jsonObject.getString("mime_type"));
                    responseDto.setCreatedBy(jsonObject.getString("created_by"));

                    // 解析创建时间
                    String createdAt = jsonObject.getString("created_at");
                    if (StringUtil.isNotBlank(createdAt)) {
                        try {
                            // 尝试解析ISO格式的时间
                            if (createdAt.contains("T")) {
                                responseDto.setCreatedAt(LocalDateTime.parse(createdAt.replace("Z", ""),
                                    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")));
                            } else {
                                // 如果是时间戳格式
                                long timestamp = Long.parseLong(createdAt);
                                responseDto.setCreatedAt(LocalDateTime.ofEpochSecond(timestamp, 0, java.time.ZoneOffset.UTC));
                            }
                        } catch (Exception e) {
                            log.warn("解析创建时间异常: {}", e.getMessage());
                        }
                    }

                    return responseDto;
                } else if (jsonObject.containsKey("error") || jsonObject.containsKey("message")) {
                    // 如果响应中包含错误信息
                    String errorMsg = jsonObject.getString("error");
                    if (StringUtil.isBlank(errorMsg)) {
                        errorMsg = jsonObject.getString("message");
                    }
                    log.error("Dify文件上传失败，错误信息: {}", errorMsg);
                    throw new ServiceException("文件上传到Dify平台失败: " + errorMsg);
                } else {
                    // 其他情况，可能是意外的响应格式
                    log.error("Dify文件上传失败，响应格式异常: {}", responseBody);
                    throw new ServiceException("文件上传到Dify平台失败，响应格式异常");
                }
            } catch (Exception e) {
                if (e instanceof ServiceException) {
                    throw e;
                }
                log.error("Dify文件上传响应解析异常", e);
                throw new ServiceException("文件上传到Dify平台失败，响应解析异常: " + e.getMessage());
            }
        } catch (Exception e) {
            log.error("Dify文件上传异常", e);
            throw new ServiceException("文件上传到Dify平台异常: " + e.getMessage());
        }
    }

    public SseEmitter createConnection(String userId) {
        // 设置超时时间为30分钟
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);
        EMITTER_MAP.put(userId, emitter);

        // 添加完成回调
        emitter.onCompletion(() -> EMITTER_MAP.remove(userId));
        emitter.onTimeout(() -> EMITTER_MAP.remove(userId));
        emitter.onError(e -> EMITTER_MAP.remove(userId));

        try {
            emitter.send(SseEmitter.event()
                .name("connect")
                .data("Connected successfully!", MediaType.APPLICATION_JSON));
        } catch (IOException e) {
            log.error("SSE connection error", e);
        }

        return emitter;
    }
}
