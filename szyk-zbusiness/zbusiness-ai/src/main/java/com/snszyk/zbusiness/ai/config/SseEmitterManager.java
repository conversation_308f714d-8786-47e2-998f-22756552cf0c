package com.snszyk.zbusiness.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE连接管理器
 * 负责管理所有客户端的SSE连接
 */
@Slf4j
public class SseEmitterManager {
    // 默认超时时间设置为30分钟
    private static final long DEFAULT_TIMEOUT = 30 * 60 * 1000L;

    // 使用ConcurrentHashMap存储所有活动的SSE连接
    private static final Map<String, SseEmitter> EMITTERS = new ConcurrentHashMap<>();

    /**
     * 创建新的SSE连接
     *
     * @param clientId 客户端ID
     * @return 新创建的SseEmitter实例
     */
    public static SseEmitter createEmitter(String clientId) {
        return createEmitter(clientId, DEFAULT_TIMEOUT);
    }

    /**
     * 创建新的SSE连接，并指定超时时间
     *
     * @param clientId 客户端ID
     * @param timeout 超时时间（毫秒）
     * @return 新创建的SseEmitter实例
     */
    public static SseEmitter createEmitter(String clientId, long timeout) {
        // 移除可能存在的旧连接
        removeEmitter(clientId);

        SseEmitter emitter = new SseEmitter(timeout);

        // 设置完成回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成: {}", clientId);
            removeEmitter(clientId);
        });

        // 设置超时回调
        emitter.onTimeout(() -> {
            log.info("SSE连接超时: {}", clientId);
            removeEmitter(clientId);
        });

        // 设置错误回调
        emitter.onError(ex -> {
            log.error("SSE连接发生错误: {}", clientId, ex);
            removeEmitter(clientId);
        });

        // 存储新创建的连接
        EMITTERS.put(clientId, emitter);
        log.info("创建新的SSE连接: {}", clientId);

        return emitter;
    }

    /**
     * 向指定客户端发送事件
     *
     * @param clientId 客户端ID
     * @param eventName 事件名称
     * @param data 事件数据
     * @return 发送是否成功
     */
    public static boolean sendEvent(String clientId, String eventName, Object data) {
        SseEmitter emitter = EMITTERS.get(clientId);
        if (emitter == null) {
            log.warn("客户端不存在或已断开连接: {}", clientId);
            return false;
        }

        try {
            emitter.send(SseEmitter.event()
                    .name(eventName)
                    .data(data));
            log.debug("向客户端 {} 发送事件 {}: {}", clientId, eventName, data);
            return true;
        } catch (IOException e) {
            log.error("向客户端 {} 发送事件失败", clientId, e);
            removeEmitter(clientId);
            return false;
        }
    }

    /**
     * 向所有客户端广播事件
     *
     * @param eventName 事件名称
     * @param data 事件数据
     */
    public static void broadcastEvent(String eventName, Object data) {
        EMITTERS.forEach((clientId, emitter) -> {
            try {
                emitter.send(SseEmitter.event()
                        .name(eventName)
                        .data(data));
                log.debug("广播事件 {} 到客户端 {}: {}", eventName, clientId, data);
            } catch (IOException e) {
                log.error("向客户端 {} 广播事件失败", clientId, e);
                removeEmitter(clientId);
            }
        });
    }

    /**
     * 移除指定客户端的SSE连接
     *
     * @param clientId 客户端ID
     */
    public static void removeEmitter(String clientId) {
        SseEmitter emitter = EMITTERS.remove(clientId);
        if (emitter != null) {
            try {
                emitter.complete();
                log.info("移除SSE连接: {}", clientId);
            } catch (Exception e) {
                log.error("关闭SSE连接时发生错误: {}", clientId, e);
            }
        }
    }

    /**
     * 获取当前活动的SSE连接数量
     *
     * @return 活动连接数
     */
    public static int getActiveEmitterCount() {
        return EMITTERS.size();
    }

    /**
     * 检查指定客户端是否有活动的SSE连接
     *
     * @param clientId 客户端ID
     * @return 是否有活动连接
     */
    public static boolean hasActiveEmitter(String clientId) {
        return EMITTERS.containsKey(clientId);
    }

    /**
     * 关闭所有SSE连接
     */
    public static void shutdownAllEmitters() {
        log.info("关闭所有SSE连接，当前连接数: {}", EMITTERS.size());
        EMITTERS.forEach((clientId, emitter) -> {
            try {
                emitter.complete();
            } catch (Exception e) {
                log.error("关闭SSE连接时发生错误: {}", clientId, e);
            }
        });
        EMITTERS.clear();
    }
}
