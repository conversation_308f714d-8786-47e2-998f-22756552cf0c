package com.snszyk.zbusiness.basic.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.utils.CollectSensorDataInfluxdb;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.service.ISensorDataService;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 传感器数据 控制器
 * <AUTHOR>
 * @since 2024-02-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sensor-data")
@Api(value = "传感器数据", tags = "传感器数据接口")
public class SensorDataController {

	private final ISensorDataService sensorDataService;

	/**
	 * 分页 传感器原始数据表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "sensorCode", value = "传感器实例编码", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "category", value = "传感器类型", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "sampleDataType", value = "采样数据类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "hasWaveData", value = "是否有波形数据（0：无波形数据；1：有波形数据；）", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "invalid", value = "是否异常（0-正常；1-异常）", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "startTime", value = "开始时间（yyyy-MM-dd HH:mm:ss）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endTime", value = "结束时间（yyyy-MM-dd HH:mm:ss）", paramType = "query", dataType = "string"),
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页（按部位查）", notes = "传入sensorDataVO")
	public R<IPage<SensorDataDTO>> page(@ApiIgnore SensorDataVO vo, Query query) {
		JSONObject object = (JSONObject)JSONObject.toJSON(vo);
		IPage<SensorDataDTO> page = Condition.getPage(query);
		Calendar c = Calendar.getInstance();
		Long end = c.getTimeInMillis();
		c.add(Calendar.YEAR,-1);
		Long begin = c.getTimeInMillis();
		if(vo.getStartTime() != null){
			begin = DateUtil.parse(vo.getStartTime(),DateUtil.PATTERN_DATETIME).getTime();
		}
		if(vo.getEndTime() != null){
			end = DateUtil.parse(vo.getEndTime(),DateUtil.PATTERN_DATETIME).getTime();
		}
		List<JSONObject> result = influxdb.queryTable(begin,end,"sensor_raw_data",object,page);
		if(result == null){
			return R.data(null);
		}
		List<SensorDataDTO> data = new ArrayList<>();
		result.forEach(item->{
			SensorDataDTO dto = JSONObject.toJavaObject(item,SensorDataDTO.class);
			dto.setSampleDataTypeName(SampledDataTypeEnum.getByCode(dto.getSampleDataType()).getName());
			data.add(dto);
		});
		page.setRecords(data);
		return R.data(page);
	}

	@Resource
	private CollectSensorDataInfluxdb influxdb;

}
