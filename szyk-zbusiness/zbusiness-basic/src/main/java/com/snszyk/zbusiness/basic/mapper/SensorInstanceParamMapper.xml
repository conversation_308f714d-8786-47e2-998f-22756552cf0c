<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.SensorInstanceParamMapper">

    <select id="getStateParam" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO">
        SELECT p.*
        FROM basic_sensor_instance_param p
        LEFT JOIN basic_sensor_instance i ON p.instance_id = i.id
        WHERE i.`code` = #{sensorCode} AND p.sample_data_type = #{sampleDataType}
    </select>

</mapper>
