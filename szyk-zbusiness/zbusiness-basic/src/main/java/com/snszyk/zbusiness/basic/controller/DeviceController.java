/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.DeviceCollectionStationDTO;
import com.snszyk.zbusiness.basic.dto.DeviceCoordinateDTO;
import com.snszyk.zbusiness.basic.dto.DeviceDTO;
import com.snszyk.zbusiness.basic.entity.Device;
import com.snszyk.zbusiness.basic.service.IDeviceService;
import com.snszyk.zbusiness.basic.service.logic.DeviceLogicService;
import com.snszyk.zbusiness.basic.vo.DeviceCollectionStationVO;
import com.snszyk.zbusiness.basic.vo.DeviceVO;
import com.snszyk.zbusiness.basic.vo.SwitchSortVO;
import com.snszyk.system.vo.DelResultVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 设备位置表控制器
 *
 * <AUTHOR>
 * @date 2023/10/18 15:16
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/device")
@Api(value = "设备位置表", tags = "设备位置表接口")
public class DeviceController extends SzykController {

	private final IDeviceService deviceService;
	private final DeviceLogicService deviceLogicService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<DeviceDTO> detail(Long id) {
		return R.data(deviceLogicService.detail(id));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入device")
	public R<List<DeviceVO>> list(@ApiIgnore @RequestParam Map<String, Object> device, SzykUser szykUser) {
		QueryWrapper<Device> queryWrapper = Condition.getQueryWrapper(device, Device.class);
		List<Device> list = deviceService.list((!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Device::getTenantId, szykUser.getTenantId()) : queryWrapper);
		return R.data(null);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增或修改", notes = "传入device")
	public R submit(@Valid @RequestBody DeviceVO device) {
		if (deviceLogicService.submit(device)) {
			CacheUtil.clear(SYS_CACHE);
			// 返回懒加载树更新节点所需字段
			Kv kv = Kv.create().set("id", String.valueOf(device.getId())).set("tenantId", device.getTenantId());
			return R.data(kv);
		}
		return R.fail("操作失败");
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(deviceService.removeDevice(Func.toLongList(ids)));
	}

	/**
	 * 校验并删除
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "校验并删除", notes = "传入ids")
	public R<DelResultVO> checkAndRemove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		DelResultVO delResultVO = deviceService.checkAndRemoveDevice(Func.toLongList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(delResultVO);
		result.setSuccess(delResultVO.getFailureNumber() == 0);
		return result;
	}

	/**
	 * 设备视图查询
	 */
	@GetMapping("device-view")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "设备视图查询", notes = "设备视图查询")
	public R<DeviceCoordinateDTO> deviceView(@ApiParam(value = "主键", required = true)@RequestParam Long id){
		DeviceCoordinateDTO dto = deviceLogicService.deviceView(id);
		return R.data(dto);
	}

	/**
	 * 绑定厂区和采集站
	 */
	@GetMapping("/bindCollectionStation")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deviceId", value = "厂区id", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "stationId", value = "采集站id", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "coordinate", value = "采集站在厂区模型图的坐标", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "绑定厂区和采集站", notes = "传入vo")
	public R<DeviceCollectionStationDTO> bindCollectionStation(@ApiIgnore DeviceCollectionStationVO vo) {
		return R.data(deviceLogicService.bindCollectionStation(vo));
	}

	/**
	 * 删除厂区绑定的单个采集站
	 * @return
	 */
	@PostMapping("/removeCollectionStation")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除厂区绑定的单个采集站", notes = "传入id")
	public R<Boolean> removeCollectionStation(String id) {
		return R.status(deviceLogicService.removeCollectionStation(id));
	}

	/**
	 * 删除厂区绑定的所有采集站
	 * @return
	 */
	@PostMapping("/removeAllCollectionStation")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "删除厂区绑定的所有采集站", notes = "传入deviceId")
	public R<Boolean> removeAllCollectionStation(String deviceId) {
		return R.status(deviceLogicService.removeAllCollectionStation(deviceId));
	}

	/**
	 * 交换地点、设备、测点的排序顺序
	 * @return
	 */
	@PostMapping("/switchSort")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "交换地点、设备、测点的排序顺序", notes = "传入类型和交换的2个对象信息")
	public R<Boolean> switchSort(@RequestBody SwitchSortVO vo) {
		return R.status(deviceLogicService.switchSort(vo));
	}

}
