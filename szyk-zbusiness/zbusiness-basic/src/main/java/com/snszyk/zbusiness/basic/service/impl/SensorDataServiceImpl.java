package com.snszyk.zbusiness.basic.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.SampleTimeDTO;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.mapper.SensorDataMapper;
import com.snszyk.zbusiness.basic.service.ISensorDataService;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import com.snszyk.zbusiness.basic.vo.WaveFormVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 传感器数据 服务接口
 *  <AUTHOR>
 */
@Service
@AllArgsConstructor
public class SensorDataServiceImpl extends ServiceImpl<SensorDataMapper, SensorData> implements ISensorDataService {

	@Resource
	private InfluxdbTools influxdbTools;
	private static final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

	private Long dateFormat(String date) throws ParseException {
		if(date == null){
			return null;
		}
		return format.parse(date).getTime();
	}
	@Override
	public IPage<SensorDataDTO> page(IPage<SensorDataDTO> page, SensorDataVO vo) {
		vo.setHasWaveData(vo.getShowWave());
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(vo);
		try{
			Long begin = dateFormat(vo.getStartTime());
			Long end = dateFormat(vo.getEndTime());
			List<JSONObject> list = influxdbTools.queryData(begin, end, jsonObject, page);
			List<SensorDataDTO> datas = new ArrayList<>();
			list.forEach(item->{
				SensorDataDTO dto = item.toJavaObject(SensorDataDTO.class);
				dto.setSampleDataTypeName(SampledDataTypeEnum.getByCode(dto.getSampleDataType()).getName());
				if(Func.isNotEmpty(dto.getSensorInstance())){
					SensorInstanceDTO instanceDTO = JSONObject.parseObject(dto.getSensorInstance(),SensorInstanceDTO.class);
					dto.setCategoryName(instanceDTO.getName());
				}
				datas.add(dto);
			});
			page.setRecords(datas);
		}catch (Exception e){
			e.printStackTrace();
		}

		return page;
	}

	@Override
	public List<SensorDataDTO> queryCharacterData(SensorDataVO vo) {
		try{
			Long begin = dateFormat(vo.getStartTime());
			Long end = dateFormat(vo.getEndTime());
			List<JSONObject> list = influxdbTools.queryData(begin, end, (JSONObject) JSONObject.toJSON(vo));
			List<SensorDataDTO> datas = new ArrayList<>();
			list.forEach(item->datas.add(item.toJavaObject(SensorDataDTO.class)));
			return datas;
		}catch (Exception e){
			log.error(e.getMessage());
		}


		return null;
	}

	@Override
	public IPage<SampleTimeDTO> sampleTimePageByWave(IPage<SampleTimeDTO> page, SensorDataVO vo) {
		Long begin = null;
		Long end = null;
		try{
			begin = dateFormat(vo.getStartTime());
			end = dateFormat(vo.getEndTime());
		}catch (Exception e){
			log.error(e.getMessage());
		}
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(vo);
		List<JSONObject> list = influxdbTools.queryData(begin,end,jsonObject,page);
		List<SampleTimeDTO> datas = new ArrayList<>();
		list.forEach(item->datas.add(new SampleTimeDTO(item.toJavaObject(SensorData.class))));
		return page.setRecords(datas);

	}

	@Override
	public List<SensorDataDTO> queryCommonCharacters(Long monitorId, Long waveId, String dataType, String startDate, String endDate) {
		try{
			Long begin = dateFormat(startDate);
			Long end = dateFormat(endDate);
			JSONObject vo = new JSONObject();
			vo.put("waveId",waveId);
			List<JSONObject> list = influxdbTools.queryData(begin, end, vo);
			List<SensorDataDTO> datas = new ArrayList<>();
			list.forEach(item->datas.add(item.toJavaObject(SensorDataDTO.class)));
			return datas;
		}catch (Exception e){
			log.error(e.getMessage());
		}
		return null;
	}

	@Override
	public List<SensorDataDTO> specificChromatogram(WaveFormVO vo) {
		try{
			Long begin = dateFormat(vo.getStartDate());
			Long end = dateFormat(vo.getEndDate());
			List<JSONObject> list = influxdbTools.queryData(begin, end, (JSONObject) JSONObject.toJSON(vo));
			List<SensorDataDTO> datas = new ArrayList<>();
			list.forEach(item->datas.add(item.toJavaObject(SensorDataDTO.class)));
			return datas;
		}catch (Exception e){
			log.error(e.getMessage());
		}

		return null;
	}

	@Override
	public List<SensorDataDTO> energyList(SensorDataVO vo) {
		return queryCharacterData(vo);
	}

	@Override
	public SensorDataDTO queryRealRev(List<Wave> waveList, String originTime) {
		SensorDataDTO result = null;
		for(Wave wave : waveList){
			SensorDataVO sensorData = new SensorDataVO(wave.getId(), null);
			JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
			Date date = DateUtil.parse(originTime, DateUtil.PATTERN_DATETIME);
			List<JSONObject> forwardList = influxdbTools.queryData(date.getTime() - 24*60*60*1000, date.getTime(), jsonObject, (query)->{
				query.addSort("_time", true);query.getQuerySQL().append(" |>limit(n:1)");
			});
			List<JSONObject> backwardList = influxdbTools.queryData(date.getTime(), date.getTime() + 24*60*60*1000, jsonObject, (query)->{
				query.addSort("_time", false);query.getQuerySQL().append(" |>limit(n:1)");
			});
			List<SensorDataDTO> forwardDataList = new ArrayList<>();
			List<SensorDataDTO> backwardDataList = new ArrayList<>();
			forwardList.forEach(item -> forwardDataList.add(item.toJavaObject(SensorDataDTO.class)));
			backwardList.forEach(item -> backwardDataList.add(item.toJavaObject(SensorDataDTO.class)));
			Date date1 = null;
			Date date2 = null;
			if(Func.isNotEmpty(forwardDataList)){
				date1 = forwardDataList.get(0).getOriginTime();
			}
			if(Func.isNotEmpty(backwardDataList)){
				date2 = backwardDataList.get(0).getOriginTime();
			}
			if(date1 == null && date2 == null){
				break;
			}
			if(date1 != null && date2 != null){
				Long a = Math.abs(forwardDataList.get(0).getOriginTime().getTime() - date.getTime());
				Long b = Math.abs(backwardDataList.get(0).getOriginTime().getTime() - date.getTime());
				if(a < b){
                    result = forwardDataList.get(0);
                } else {
                    result = backwardDataList.get(0);
                }
			} else {
				result = date1 != null ? forwardDataList.get(0) : backwardDataList.get(0);
			}
		}
		return result;
	}

}
