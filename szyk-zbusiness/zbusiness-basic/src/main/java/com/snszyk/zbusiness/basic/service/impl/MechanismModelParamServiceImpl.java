/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.zbusiness.basic.entity.MechanismModelParam;
import com.snszyk.zbusiness.basic.mapper.MechanismModelParamMapper;
import com.snszyk.zbusiness.basic.service.IMechanismModelParamService;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;
import org.springframework.stereotype.Service;

/**
 * 机理模型参数表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Service
public class MechanismModelParamServiceImpl extends ServiceImpl<MechanismModelParamMapper, MechanismModelParam> implements IMechanismModelParamService {

	@Override
	public IPage<MechanismModelParamVO> selectMechanismModelParamPage(IPage<MechanismModelParamVO> page, MechanismModelParamVO mechanismModelParam) {
		return page.setRecords(baseMapper.selectMechanismModelParamPage(page, mechanismModelParam));
	}

}
