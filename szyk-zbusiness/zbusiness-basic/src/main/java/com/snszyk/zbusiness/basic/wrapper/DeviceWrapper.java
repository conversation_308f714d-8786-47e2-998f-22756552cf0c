/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.basic.entity.Device;
import com.snszyk.zbusiness.basic.vo.DeviceVO;

import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class DeviceWrapper extends BaseEntityWrapper<Device, DeviceVO> {

	public static DeviceWrapper build() {
		return new DeviceWrapper();
	}

	@Override
	public DeviceVO entityVO(Device device) {
		DeviceVO deviceVO = Objects.requireNonNull(BeanUtil.copy(device, DeviceVO.class));

		return deviceVO;
	}

}
