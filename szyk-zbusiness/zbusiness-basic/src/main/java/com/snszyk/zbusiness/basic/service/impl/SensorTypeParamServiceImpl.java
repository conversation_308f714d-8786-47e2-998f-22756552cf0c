/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.zbusiness.basic.entity.SensorTypeParam;
import com.snszyk.zbusiness.basic.mapper.SensorTypeParamMapper;
import com.snszyk.zbusiness.basic.service.ISensorTypeParamService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 传感器类型参数表 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SensorTypeParamServiceImpl extends ServiceImpl<SensorTypeParamMapper, SensorTypeParam> implements ISensorTypeParamService {


}
