package com.snszyk.zbusiness.basic.rabbit.bussiness;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.rabbit.station.ScheduleTimerTask;
import com.snszyk.zbusiness.basic.rabbit.station.SendWarinBusiness;
import com.snszyk.zbusiness.basic.service.ICollectionStationChannelService;
import com.snszyk.zbusiness.basic.service.ICollectionStationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.snszyk.common.constant.EolmConstant.Cache.COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT;

/**
 * 采集站在线状态业务
 */
@Slf4j
@Component
public class StationStateBusiness extends AbstractBusiness{
	@Override
	public String getCommand() {
		return Command.VIBRATE_COMMAND;
	}
	@Resource
	private SendWarinBusiness business;
	@Resource
	private SzykRedis szykRedis;
	private ScheduledExecutorService executor = Executors.newScheduledThreadPool(500);
	@Resource
	private ICollectionStationChannelService channelService;
	@Resource
	private ICollectionStationService stationService;
	private LinkedHashMap<String, ScheduledFuture> scheduledFutures = new LinkedHashMap<>();
	@PostConstruct
	public void initStationConfig(){
		List<CollectionStation> list =  stationService.list();
		list.forEach(station -> {
			ScheduleTimerTask timerTask = new ScheduleTimerTask(Func.toStr(station.getId()), business, true);
			ScheduledFuture future = executor.schedule(timerTask, COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT, TimeUnit.MINUTES);
			scheduledFutures.put(station.getId().toString(), future);
		});
		List<CollectionStationChannel> channelList = this.channelService.list(new QueryWrapper<CollectionStationChannel>()
			.lambda().eq(CollectionStationChannel::getOnline, 1));
		channelList.forEach(station -> {
			ScheduleTimerTask timerTask = new ScheduleTimerTask(station.getSensorCode(),business, false);
			scheduledFutures.put(station.getSensorCode(),executor.schedule(timerTask, COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT, TimeUnit.MINUTES));
		});
	}

	private void cancelFuture(String sensorCode,String station){
		ScheduledFuture future = scheduledFutures.get(station);
		if(future != null){
			boolean flag = future.cancel(false);
			log.info("设置停止采集站:{}",flag);
		}
		future = scheduledFutures.get(sensorCode);
		if(future != null){
			boolean flag = future.cancel(false);
			log.info("设置停止传感器：{}", flag);
		}
	}
	private void initStation(MessageBean message){
		log.info("开始更新采集站状态及传感器在线状态");
		List<CollectionStationChannel> channelList = channelService.list(new QueryWrapper<CollectionStationChannel>()
			.lambda().eq(CollectionStationChannel::getSensorCode, message.getId()));
		if (Func.isEmpty(channelList)){
			log.error("传感器没有绑定采集站，退出");
			return;
		}
		channelList.forEach(channel -> {
			CollectionStation station = new CollectionStation();
			if(channel.getStationId() != null){
				log.debug("当前采集站ID:{}", channel.getStationId());
				station.setId(channel.getStationId());
				station.setOnline(1);
				stationService.updateById(station);
				cancelFuture(channel.getSensorCode(),Func.toStr(channel.getStationId()));
				scheduledFutures.put(Func.toStr(channel.getStationId()), executor.schedule(new ScheduleTimerTask(Func.toStr(channel.getStationId()),business,true), COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT, TimeUnit.MINUTES));
			}
			channel.setOnline(1);
			channelService.updateById(channel);
			scheduledFutures.put(channel.getSensorCode(), executor.schedule(new ScheduleTimerTask(Func.toStr(channel.getSensorCode()),business,false), COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT, TimeUnit.MINUTES));
		});
		// 只要有数据，则传感器在线
		String key = message.getId() + StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode();
		szykRedis.setEx(key, 1,
			Duration.ofMinutes(COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT));
		log.info("更新采集站在线状态成功");
	}

	@Override
	public void business(MessageBean message) {
		if (!SampledDataTypeEnum.ACCELERATION.getCode().equals(message.getType())) {
			log.debug("通过是否发送加速度值判断是否在线-减少数据处理量（其他值也可以实现）");
			return;
		}
		initStation(message);
	}
}
