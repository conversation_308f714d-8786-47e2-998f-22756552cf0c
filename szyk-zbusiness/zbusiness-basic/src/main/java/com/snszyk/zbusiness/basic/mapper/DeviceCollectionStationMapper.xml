<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.DeviceCollectionStationMapper">

    <update id="deleteByDeviceId">
        UPDATE eolm_device_collection_station
        SET is_deleted = 1
        WHERE device_id = #{deviceId}
    </update>

    <select id="listCollectionStation" resultType="com.snszyk.zbusiness.basic.dto.DeviceCollectionStationDTO">
        SELECT dcs.*, cs.`name` AS station_name, cs.online
        FROM eolm_device_collection_station dcs
        LEFT JOIN eolm_collection_station cs ON dcs.station_id = cs.id
        WHERE dcs.device_id = #{deviceId} AND dcs.is_deleted = 0;
    </select>

</mapper>
