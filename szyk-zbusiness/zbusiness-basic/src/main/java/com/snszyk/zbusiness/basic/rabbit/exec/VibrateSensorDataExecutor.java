package com.snszyk.zbusiness.basic.rabbit.exec;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dsp.DspUtil;
import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.InvalidEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.ISensorInstanceParamService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 波形类型（加速度、速度、位移、温度）传感器数据保存
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class VibrateSensorDataExecutor extends AbstractSensorDataExecutor{


	private final ISensorInstanceParamService sensorInstanceParamService;

	@Override
	public String getCommand() {
		// 针对加速度、速度、位移、温度4种数据类型
		return Command.VIBRATE_COMMAND;
	}

	@Override
	protected SensorData createSensorData(MessageBean message, SensorData sensorData, Wave wave){
		SampledDataTypeEnum e = SampledDataTypeEnum.getByCode(message.getType());
		if(SampledDataTypeEnum.EQUIPMENT_TEMPERATURE == e ){
			sensorData.setSamplingFreq(null)
				.setSamplingPoints(null)
				.setRmsValue(null)
				.setPeakValue(null)
				.setPeakPeakValue(null)
				.setClearanceValue(null)
				.setSkewnessValue(null)
				.setKurtosisValue(null);
			return sensorData;
		}
		JSONArray waveArray = JSON.parseArray(message.getWave());
		double[] waveData = jsonArray2DoubleArray(waveArray);
		if(Func.isEmpty(message.getWave()) || Func.isEmpty(waveData)){
			return sensorData;
		}

		//校验采样频率：非温度的振动类型
		SensorInstanceParam param = sensorInstanceParamService.getById(wave.getSensorInstanceParamId());
		if (param.getSamplingFreq().multiply(BigDecimal.valueOf(1000))
			.compareTo(BigDecimal.valueOf(message.getSamplingFreq())) != 0
			|| !param.getSamplingPoints().equals(message.getSamplingPoints())) {
			sensorData.setInvalid(InvalidEnum.INVALID.getCode());
			sensorData.setInvalidReason(EolmConstant.InvalidReason.INCONSISTENT_SAMPLING_FREQ);
		}
		log.info("【开始】计算6种通用指标...");
		long startTimeWave = System.currentTimeMillis();


		// 校验采样点数：非温度的振动类型
		if (Func.isNotEmpty(message.getSamplingPoints()) && waveData.length != message.getSamplingPoints()) {
			sensorData.setInvalid(InvalidEnum.INVALID.getCode());
			sensorData.setInvalidReason(EolmConstant.InvalidReason.INCONSISTENT_SAMPLING_POINTS);
		}

		if (sensorData.getRmsValue() == null || BigDecimal.ZERO.compareTo(sensorData.getRmsValue()) == 0) {
			BigDecimal bigDecimal = BigDecimal.valueOf(DspUtil.getRMS(waveData));
			sensorData.setRmsValue(bigDecimal);
			sensorData.setValue(bigDecimal);
			message.setValue(sensorData.getRmsValue().toString());
			message.setEffective(sensorData.getRmsValue().floatValue());
			message.setValue(bigDecimal.toString());
		}
		if (sensorData.getPeakValue() == null || BigDecimal.ZERO.compareTo(sensorData.getPeakValue()) == 0) {
			sensorData.setPeakValue(BigDecimal.valueOf(DspUtil.getPeak(waveData)));
			message.setPeak(sensorData.getPeakValue().floatValue());
		}
		if (sensorData.getPeakPeakValue() == null || BigDecimal.ZERO.compareTo(sensorData.getPeakPeakValue()) == 0) {
			sensorData.setPeakPeakValue(BigDecimal.valueOf(DspUtil.getPeakToPeak(waveData)));
			message.setPeakPeak(sensorData.getPeakPeakValue().floatValue());
		}
		if (sensorData.getClearanceValue() == null || BigDecimal.ZERO.compareTo(sensorData.getClearanceValue()) == 0) {
			sensorData.setClearanceValue(BigDecimal.valueOf(DspUtil.getClearanceFactor(waveData)));
			message.setClearance(sensorData.getClearanceValue().floatValue());
		}
		if (sensorData.getSkewnessValue() == null || BigDecimal.ZERO.compareTo(sensorData.getSkewnessValue()) == 0) {
			sensorData.setSkewnessValue(BigDecimal.valueOf(DspUtil.getSkewnessFactor(waveData)));
			message.setSkewness(sensorData.getSkewnessValue().floatValue());
		}
		if (sensorData.getKurtosisValue() == null || BigDecimal.ZERO.compareTo(sensorData.getKurtosisValue()) == 0) {
			sensorData.setKurtosisValue(BigDecimal.valueOf(DspUtil.getKurtosis(waveData)));
			message.setKurtosis(sensorData.getKurtosisValue().floatValue());
		}
		log.info("【结束】计算6种通用指标。耗时：{}ms", System.currentTimeMillis() - startTimeWave);

		sensorData.setHasWaveData(1);
		return sensorData;
	}


	/**
	 * jsonArray数组转double数组
	 * @param jsonArray 原数据
	 * @return double数组
	 */
	private double[] jsonArray2DoubleArray(JSONArray jsonArray) {
		if (jsonArray == null || jsonArray.size() == 0) {
			return null;
		}

		double[] data = new double[jsonArray.size()];
		for (int i = 0; i < jsonArray.size(); i++) {
			data[i] = jsonArray.getDoubleValue(i);
		}

		return data;
	}


}
