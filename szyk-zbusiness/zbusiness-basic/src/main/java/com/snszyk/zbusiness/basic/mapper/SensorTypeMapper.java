/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.SensorTypeDTO;
import com.snszyk.zbusiness.basic.entity.SensorType;
import com.snszyk.zbusiness.basic.vo.SensorTypeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 传感器类型表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface SensorTypeMapper extends BaseMapper<SensorType> {


	/**
	 * 自定义分页
	 * @param page page
	 * @param vo vo
	 * @return
	 */
    List<SensorTypeDTO> page(IPage<SensorTypeDTO> page, @Param("vo") SensorTypeVO vo);
}
