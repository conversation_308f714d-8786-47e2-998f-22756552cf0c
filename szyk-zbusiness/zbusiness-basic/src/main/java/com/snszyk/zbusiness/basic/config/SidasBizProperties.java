package com.snszyk.zbusiness.basic.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * sidas业务配置
 *
 * <AUTHOR>
 * @date 2024/05/06 10:09
 **/
@Data
@ConfigurationProperties(prefix = "szyk.eolm")
public class SidasBizProperties {

	/**
	 * 是否根据振动波形的有效值判断设备运行状态
	 */
	private boolean enableRunningByVibration;

	/**
	 * 手动设置停机线
	 */
	private double runningStateMin;

	/**
	 * 采样数据异常时间间隔
	 */
	private int sampleDataExceptionTime;


}
