/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.basic.dto.BasicTreeNameUpdateDTO;
import com.snszyk.zbusiness.basic.dto.MonitorDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.service.*;
import com.snszyk.zbusiness.basic.vo.*;
import com.snszyk.zbusiness.ops.service.IAlarmDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测点信息表 逻辑服务实现类
 *
 * <AUTHOR>
 * @date 2022/11/02 15:01
 **/
@Slf4j
@AllArgsConstructor
@Service
public class MonitorLogicService {

	private final IMonitorService monitorService;
	private final IEquipmentService equipmentService;
	private final IBasicTreeService basicTreeService;
	private final IMonitorParamService monitorParamService;
	private final IDisplayPositionService displayPositionService;
	private final ISensorInstanceService sensorInstanceService;
	private final IWaveService waveService;
	private final IWaveMarkService waveMarkService;
	private final IAttachService attachService;
	private final RabbitTemplate rabbitTemplate;
	private final IAlarmDetailService alarmDetailService;


	/**
	 * 根据设备id查询测点列表
	 *
	 * @param equipmentId
	 * @return java.util.List<com.snszyk.zbusiness.basic.dto.MonitorDTO>
	 * <AUTHOR>
	 * @date 2022/11/03 15:57
	 */
	public List<MonitorDTO> listByEquipment(Long equipmentId) {
		List<MonitorDTO> result = null;
		List<Monitor> list = monitorService.list(Wrappers.<Monitor>query().lambda()
			.eq(Monitor::getEquipmentId, equipmentId)
			.orderByAsc(Monitor::getSort));
		if (Func.isNotEmpty(list)) {
			result = list.stream().map(monitor -> {
				MonitorDTO monitorDTO = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
				monitorDTO.setMeasureDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION, monitor.getMeasureDirection()));
				monitorDTO.setEquipmentTypeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType()));
				monitorDTO.setCanEdit(Boolean.TRUE);
				// 附件信息
				if (Func.isNotEmpty(monitor.getImage())) {
					List<Attach> attachList = attachService.listByIds(Collections.singletonList(monitor.getImage()));
					if (ObjectUtil.isNotEmpty(attachList)) {
						monitorDTO.setImageList(attachList);
					}
				}
				// 绑定传感器数量
				int count = sensorInstanceService.count(Wrappers.<SensorInstance>query().lambda()
					.eq(SensorInstance::getMonitorId, monitor.getId())
					.eq(SensorInstance::getIsDeleted, 0));
				monitorDTO.setSensorNumber(count);
				return monitorDTO;
			}).collect(Collectors.toList());
		}
		return result;
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return com.snszyk.zbusiness.basic.dto.MonitorDTO
	 * <AUTHOR>
	 * @date 2022/11/02 15:10
	 */
	public MonitorDTO detail(Long id) {
		Monitor monitor = monitorService.getById(id);
		if (monitor == null) {
			throw new ServiceException("当前部位已删除，请刷新后重试!");
		}
		MonitorDTO detailDTO = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
		detailDTO.setEquipmentTypeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType()));
		// 附件信息
		if (Func.isNotEmpty(monitor.getImage())) {
			List<Attach> attachList = attachService.listByIds(Arrays.asList(monitor.getImage()));
			if (ObjectUtil.isNotEmpty(attachList)) {
				detailDTO.setImageList(attachList);
			}
		}
		// 绑定传感器数量
		int count = sensorInstanceService.count(Wrappers.<SensorInstance>query().lambda()
			.eq(SensorInstance::getMonitorId, monitor.getId())
			.eq(SensorInstance::getIsDeleted, 0));
		detailDTO.setSensorNumber(count);
		// 3D场景id
		Equipment equipment = equipmentService.getOne(Wrappers.<Equipment>query().lambda()
			.eq(Equipment::getId, monitor.getEquipmentId()).select(Equipment::getId, Equipment::getSceneId));
		detailDTO.setSceneId(equipment.getSceneId());
		return detailDTO;
	}

	/**
	 * 新增或修改测点
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/05/08 09:23
	 */
	public boolean submit(EquipmentVO vo) {
		Equipment equipment = equipmentService.getById(vo.getId());
		if (equipment == null) {
			throw new ServiceException("当前设备已删除，请刷新后重试!");
		}
		List<MonitorVO> monitorList = vo.getMonitorList();
		// 校验编码唯一性
		if (Func.isNotEmpty(monitorList)) {
			this.checkMonitorCode(monitorList);
		}
		boolean ret = false;
		if (Func.isNotEmpty(monitorList)) {
			List<Monitor> list = monitorList.stream().map(monitorVO -> {
				Monitor monitor = Objects.requireNonNull(BeanUtil.copy(monitorVO, Monitor.class));
				monitor.setEquipmentId(equipment.getId());
				if (Func.isEmpty(monitor.getId())) {
					monitor.setSort(monitorService.getMaxSort() + 1);
				} else {
					Monitor monitorInDb = monitorService.getById(monitor.getId());
					//修改测点名称 & pathName
					if (!Func.equals(monitorInDb.getName(), monitorVO.getName())) {
						// 更新设备的pathName
						monitor.setPathName(monitorInDb.getPathName().substring(0, monitorInDb.getPathName().lastIndexOf(StringPool.COMMA))
							+ StringPool.COMMA
							+ monitor.getName());
						// 发送名称更新MQ
						BasicTreeNameUpdateDTO dto = new BasicTreeNameUpdateDTO().setId(monitor.getId())
							.setNewName(monitor.getName())
							.setNewPathName(monitor.getPathName())
							.setType(BasicTreeNameUpdateDTO.TYPE_MONITOR);
						rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_BASIC_TREE_NAME_UPDATE, null, dto);
						log.info("发送名称更新MQ成功：dto = {}", dto);
					}
				}
				return monitor;
			}).collect(Collectors.toList());
			ret = monitorService.saveOrUpdateBatch(list);
			log.info("更新测点 - {}", ret);
			list.forEach(monitor -> {
				// 更新基础树结构
				BasicTreeVO basicTreeVO = new BasicTreeVO().toBasicTreeVO(monitor);
				basicTreeVO.setParentId(equipment.getId()).setNodeLevel(vo.getLevel() + 1);
				basicTreeService.submit(basicTreeVO);
				BasicTree basicTree = basicTreeService.getById(monitor.getId());
				monitorService.update(Wrappers.<Monitor>update().lambda()
					.eq(Monitor::getId, monitor.getId()).set(Monitor::getPath, basicTree.getPath()).set(Monitor::getPathName, basicTree.getPathName()));
			});
		}
		return ret;
	}

	/**
	 * 校验测点编号唯一性
	 *
	 * @param list
	 * @return void
	 * <AUTHOR>
	 * @date 2023/05/08 17:06
	 */
	public void checkMonitorCode(List<MonitorVO> list) {
		List<String> duplicateCodes = new ArrayList<>();
		list.forEach(vo -> {
			List<Monitor> monitorList = monitorService.list(Wrappers.<Monitor>query().lambda().ne(Func.isNotEmpty(vo.getId()), Monitor::getId, vo.getId()).eq(Monitor::getCode, vo.getCode()));
			if (Func.isNotEmpty(monitorList)) {
				duplicateCodes.add(vo.getCode());
			}
		});
		if (Func.isNotEmpty(duplicateCodes)) {
			String monitorCode = String.join(",", duplicateCodes);
			throw new ServiceException("重复的部位编号:" + monitorCode);
		}
	}

	/**
	 * 测点配置传感器实例 - 测点绑定的传感器实例列表
	 *
	 * @param monitorId 测点ID
	 * @return
	 */
	public List<SensorInstanceDTO> monitorSensorInstanceList(Long monitorId) {
		List<SensorInstanceDTO> result = sensorInstanceService.listByMonitorId(monitorId);
		if (Func.isNotEmpty(result)) {
			result.forEach(instanceDTO -> {
				// 传感器生产厂家
				if (Func.isNotEmpty(instanceDTO.getSupplier())) {
					instanceDTO.setSupplierName(DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, instanceDTO.getSupplier()));
				}

				// 传感器类型名称
				if (Func.isNotEmpty(instanceDTO.getCategory())) {
					instanceDTO.setCategoryName(DictBizCache.getValue(DictBizEnum.SENSOR_CATEGORY, instanceDTO.getCategory()));
				}
				instanceDTO.setCanEdit(false);
			});
		}
		return result;
	}

	/**
	 * 绑定传感器实例：更新传感器实例的monitorId、equipmentId、installDirection字段！
	 *
	 * @param monitorId          测点id
	 * @param sensorInstanceList 传感器实例列表
	 * @return boolean
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean bindSensorInstance(Long monitorId, List<SensorInstanceVO> sensorInstanceList) {
		return sensorInstanceService.bindSensorInstance(monitorId, sensorInstanceList);
	}

	/**
	 * 解绑传感器实例
	 *
	 * @param sensorInstanceVO 传感器实例
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean unbindSensorInstance(SensorInstanceVO sensorInstanceVO) {
		// 清空传感器实例的equipmentId\monitorId\installDirection\virtualSensorId
		int unbindCount = sensorInstanceService.unbindBySensorInstance(sensorInstanceVO.getId());
		log.info("unbindSensorInstance() - 解绑传感器，更新传感器实例数据：{}", unbindCount);
		// 删除波形
		List<Wave> waveList = waveService.list(Wrappers.<Wave>lambdaQuery()
			.eq(Wave::getSensorCode, sensorInstanceVO.getCode()));
		if (Func.isNotEmpty(waveList)) {
			List<Long> waveIdList = waveList.stream().map(Wave::getId).collect(Collectors.toList());
			// 删除特征频率识别wave_mark表
			waveMarkService.remove(Wrappers.<WaveMark>query().lambda().in(WaveMark::getWaveId, waveIdList));
			// 解绑波形
			int unbindWaveCount = waveService.unbindWaveByIds(waveIdList);
			log.info("unbindSensorInstance() - sensorCode = {}, 解绑波形数：{}", sensorInstanceVO.getCode(), unbindWaveCount);
		}
		// 删除门限
		log.info("unbindSensorInstance() - 删除门限：{}", sensorInstanceVO.getCode());
		sensorInstanceService.removeAlarmThresholdBySensorCode(sensorInstanceVO.getCode());
		displayPositionService.removeBySensorCode(sensorInstanceVO.getCode());
		return true;
	}

	/**
	 * 删除测点
	 *
	 * @param ids
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/05/09 09:16
	 */
	public boolean removeMonitor(List<Long> ids) {
		List<Monitor> list = monitorService.list(Wrappers.<Monitor>query().lambda().in(Monitor::getId, ids));
		if (Func.isEmpty(list)) {
			throw new ServiceException("当前部位已删除，请刷新后重试!");
		}
		//判断是否存在业务数据
		ids.forEach(id -> {
			Monitor monitor = monitorService.getOne(Wrappers.<Monitor>query().lambda()
				.eq(Monitor::getId, id).select(Monitor::getId, Monitor::getName));

			Integer alarmDetailCount = alarmDetailService.countByMonitorId(id);
			if (alarmDetailCount > 0) {
				throw new ServiceException(StringUtil.format("部位{}，存在报警数据", monitor.getName()));
			}
		});
		// 根据测点id删除传感器数据表 - 暂不删除

		// 删除门限和波形
		List<Wave> waveList = waveService.list(Wrappers.<Wave>lambdaQuery()
			.in(Wave::getMonitorId, ids));
		if (Func.isNotEmpty(waveList)) {
			// 删除门限
			List<Long> waveIdList = waveList.stream()
				.map(Wave::getId)
				.collect(Collectors.toList());
			int removeAlarmThresholdCount = waveService.removeAlarmThresholdByWave(waveIdList);
			log.info("删除测点同步删除门限：{}", removeAlarmThresholdCount);

			// 删除波形
			boolean removeWave = waveService.removeByIds(waveIdList);
			log.info("删除测点同步删除波形：{}", removeWave);
		}
		// 同步删除绑定传感器
		int unbindCount = sensorInstanceService.unbindByMonitor(ids);
		log.info("删除测点时解绑传感器：{}", unbindCount);
		// 同步删除部位参数配置
		monitorParamService.remove(Wrappers.<MonitorParam>query().lambda().in(MonitorParam::getMonitorId, ids));
		// 删除虚拟配置
		ids.forEach(monitorId -> {
			displayPositionService.removeByMonitor(monitorId);
		});
		// 删除特征频率识别wave_mark表
		waveMarkService.remove(Wrappers.<WaveMark>query().lambda().in(WaveMark::getMonitorId, ids));
		// 删除基础树节点
		basicTreeService.removeByIds(ids);
		return monitorService.removeByIds(ids);
	}

	/**
	 * 报警管理同步更新设备报警等级
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/02/17 10:17
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean updateAlarmLevel(AlarmLevelVO vo) {
		MonitorVO monitorVO = vo.getMonitor();
		if (Func.isNotEmpty(monitorVO)) {
			log.info("=====》测点报警：================{}", monitorVO.getId() + ":" + monitorVO.getAlarmLevel());
			Integer alarmLevel = monitorVO.getAlarmLevel();
			BasicTree monitorNode = basicTreeService.getById(monitorVO.getId());
			if (alarmLevel >= monitorNode.getAlarmLevel()) {
				monitorNode.setAlarmLevel(alarmLevel);
				List<BasicTree> parentNodes = basicTreeService.getNodeParent(monitorNode.getId());
				List<BasicTree> updateParentNodes = parentNodes.stream().filter(parentNode -> alarmLevel > parentNode.getAlarmLevel())
					.map(parentNode -> parentNode.setAlarmLevel(alarmLevel)).collect(Collectors.toList());
				updateParentNodes.add(monitorNode);
				basicTreeService.updateBatchById(updateParentNodes);
			}
		}
		return true;
	}

	/**
	 * 绑定传感器实例与3D传感器模型
	 */
	public boolean bindSensorInstanceModel(List<SensorInstanceVO> sensorInstanceList) {
		List<SensorInstance> list = sensorInstanceList.stream()
			.map(vo -> {
				SensorInstance sensorInstance = sensorInstanceService.getById(vo.getId());
				sensorInstance.setVirtualSensorId(vo.getVirtualSensorId());
				return sensorInstance;
			})
			.collect(Collectors.toList());
		boolean update = sensorInstanceService.updateBatchById(list);
		log.info("绑定传感器实例与3D传感器模型：{}", update);
		return update;
	}

	/**
	 * 删除虚拟传感器
	 *
	 * @param virtualSensorIds 虚拟传感器ID列表
	 * @return
	 */
	public DelResultVO removeVirtualSensor(List<String> virtualSensorIds) {
		DelResultVO resultVO = new DelResultVO();
		virtualSensorIds.forEach(id -> {
			//查询虚拟传感器绑定信息
			List<SensorInstance> refs = sensorInstanceService.list(Wrappers.<SensorInstance>query()
				.lambda()
				.eq(SensorInstance::getVirtualSensorId, id));
			if (CollectionUtil.isNotEmpty(refs)) {
				//如果已与传感器实例绑定，不允许删除，收集引用的传感器编码放入失败提示信息
				Set<String> refNameSet = refs.stream()
					.map(SensorInstance::getCode)
					.collect(Collectors.toSet());

				resultVO.getDetailVOList().add(new DelDetailVO(id, Boolean.FALSE,
					"虚拟传感器信息与真实传感器绑定，无法删除，具体如下：\r\n"
						+ StringUtil.collectionToDelimitedString(refNameSet, ",")));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
			}
		});
		return resultVO;
	}

}
