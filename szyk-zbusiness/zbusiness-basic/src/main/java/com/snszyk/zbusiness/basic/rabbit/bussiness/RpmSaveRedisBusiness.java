package com.snszyk.zbusiness.basic.rabbit.bussiness;

import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.ICollectionStationChannelService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 1、转速保存数据后，将最新的数据存到Redis
 *		key为 "sensorCode:sensorInstanceParamId:sampleDataType"
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class RpmSaveRedisBusiness extends AbstractBusiness {

	private final ICollectionStationChannelService collectionStationChannelService;
	private final SzykRedis szykRedis;

	@Override
	public String getCommand() {
		return Command.RPM_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		super.business(message);
		// 传感器数据最新数据
		if (SampledDataTypeEnum.RPM.getCode().equals(message.getType())) {
			// 转速数据
			if (message.getValue() != null) {
				// 转速最新值
				String redisKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + SampledDataTypeEnum.RPM.getCode();
				szykRedis.set(redisKey, message.getValue());

				// 测点最新转速
				String monitorRedisKey = message.getMonitorId() + StringPool.COLON + SampledDataTypeEnum.RPM.getCode();
				szykRedis.set(monitorRedisKey, message.getValue());
			}
		}
	}

}
