/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.basic.entity.DisplayPosition;
import org.apache.ibatis.annotations.Param;

/**
 * 虚拟展示位置表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface DisplayPositionMapper extends BaseMapper<DisplayPosition> {

	/**
	 * 根据设备id删除2D图位置
	 *
	 * @param equipmentId
	 * @return
	 */
	Integer removeByEquipment(@Param("equipmentId") Long equipmentId);

	/**
	 * 根据测点id删除2D图位置
	 *
	 * @param monitorId
	 * @return
	 */
	Integer removeByMonitor(@Param("monitorId") Long monitorId);

	/**
	 * 根据传感器编码删除2D图位置
	 *
	 * @param sensorCode
	 * @param category
	 * @return
	 */
	Integer removeBySensorCode(@Param("sensorCode") String sensorCode, @Param("category") Integer category);

}
