/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import com.snszyk.zbusiness.basic.mapper.SensorInstanceParamMapper;
import com.snszyk.zbusiness.basic.service.ISensorInstanceParamService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 传感器实例参数表 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SensorInstanceParamServiceImpl extends ServiceImpl<SensorInstanceParamMapper, SensorInstanceParam>
	implements ISensorInstanceParamService {

	@Override
	public SensorInstanceParamDTO getStateParam(String sensorCode, String sampleDataType) {
		return baseMapper.getStateParam(sensorCode, sampleDataType);
	}
}
