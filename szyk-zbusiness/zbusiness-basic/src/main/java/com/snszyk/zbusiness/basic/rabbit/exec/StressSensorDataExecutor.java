package com.snszyk.zbusiness.basic.rabbit.exec;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.feign.PythonServerFeign;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 应力波类型传感器数据保存
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class StressSensorDataExecutor  extends AbstractSensorDataExecutor {

	private final PythonServerFeign pythonServerFeign;
	@Override
	public String getCommand() {
		// 针对应力波数据类型
		return Command.STRESS_COMMAND;
	}


	@Override
	protected SensorData createSensorData(MessageBean message, SensorData sensorData, Wave wave) {
		// 1、调用能量值
		JSONObject dbmJsonObject = pythonServerFeign.dbm(DateUtil.format(sensorData.getOriginTime(),
				DateUtil.PATTERN_DATETIME),
				sensorData.getWaveId().toString(),
				wave.getMonitorId().toString());
		if (dbmJsonObject != null) {
			double dbm = dbmJsonObject.getDoubleValue("dbm");
			sensorData.setEnergyValue(BigDecimal.valueOf(dbm));
		}
		return sensorData;
	}
}
