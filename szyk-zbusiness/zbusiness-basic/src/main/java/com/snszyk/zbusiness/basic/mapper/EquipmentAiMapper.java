/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.EquipmentAiDTO;
import com.snszyk.zbusiness.basic.entity.EquipmentAi;
import com.snszyk.zbusiness.basic.vo.EquipmentAiVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备AI模型表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-07-08
 */
public interface EquipmentAiMapper extends BaseMapper<EquipmentAi> {

	/**
	 * AI配置分页
	 *
	 * @param page
	 * @param equipmentAi
	 * @return
	 */
	List<EquipmentAiDTO> equipmentAiPage(IPage page, @Param("equipmentAi") EquipmentAiVO equipmentAi);

	/**
	 * 获取部位应用的模型子集
	 *
	 * @param monitorId
	 * @param modelId
	 * @return
	 */
	List<EquipmentAiDTO> getChildrenList(@Param("monitorId")Long monitorId, @Param("modelId") Long modelId);

	/**
	 * 根据设备id物理删除
	 *
	 * @param equipmentId
	 * @return
	 */
	Integer removeByEquipment(@Param("equipmentId") Long equipmentId);

	/**
	 * 根据测点id物理删除
	 *
	 * @param monitorId
	 * @return
	 */
	Integer removeByMonitor(@Param("monitorId") Long monitorId);

	/**
	 * 设备解除引用AI模型
	 *
	 * @param equipmentId
	 * @param aiModelId
	 * @return
	 */
	Integer removeEquipmentAi(@Param("equipmentId") Long equipmentId, @Param("aiModelId") Long aiModelId);

	/**
	 * 根据传感器编码物理删除
	 *
	 * @param sensorCode
	 * @return
	 */
	Integer removeBySensorCode(@Param("sensorCode") String sensorCode);

}
