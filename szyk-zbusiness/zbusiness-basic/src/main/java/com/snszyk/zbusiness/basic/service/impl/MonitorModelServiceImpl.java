/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.zbusiness.basic.dto.MechanismModelDTO;
import com.snszyk.zbusiness.basic.dto.MonitorModelDTO;
import com.snszyk.zbusiness.basic.entity.MechanismModelParam;
import com.snszyk.zbusiness.basic.entity.MonitorModel;
import com.snszyk.zbusiness.basic.mapper.MechanismModelMapper;
import com.snszyk.zbusiness.basic.mapper.MonitorModelMapper;
import com.snszyk.zbusiness.basic.service.IMechanismModelParamService;
import com.snszyk.zbusiness.basic.service.IMonitorModelService;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;
import com.snszyk.zbusiness.basic.vo.ModelParamVO;
import com.snszyk.zbusiness.basic.vo.MonitorModelVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 部位机理模型表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
@Service
@AllArgsConstructor
public class MonitorModelServiceImpl extends ServiceImpl<MonitorModelMapper, MonitorModel> implements IMonitorModelService {

	private final MechanismModelMapper mechanismModelMapper;
	private final IMechanismModelParamService mechanismModelParamService;
	private final IDictBizService dictBizService;

	@Override
	public IPage<MonitorModelDTO> page(IPage<MonitorModelDTO> page, MonitorModelVO monitorModel) {
		List<MonitorModelDTO> list = baseMapper.page(page, monitorModel);
		if (Func.isNotEmpty(list)) {
			list.forEach(dto -> {
				dto.setMonitorId(dto.getId());
				dto.setPathName(dto.getPathName().substring(0, dto.getPathName().lastIndexOf(StringPool.COMMA))
					.replace(StringPool.COMMA, StringPool.SLASH));
				if (Func.isNotEmpty(dto.getModelId())) {
					dto.setApplyEquipmentName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, dto.getApplyEquipment()))
						.setApplyPowerName(DictBizCache.getValue(DictBizEnum.POWER_RANGE, dto.getApplyPower()))
						.setTypeName(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, dto.getType()))
						.setApplyDataName(DictBizCache.getValue(DictBizEnum.DATA_TYPE, dto.getApplyData()));
					DictBiz dictBiz = dictBizService.getDictValue(DictBizEnum.MODEL_TYPE.getName(), Func.toStr(dto.getType()));
					if (Func.isNotEmpty(dictBiz)) {
						dto.setRemark(dictBiz.getRemark());
					}
					dto.setModelStatus(mechanismModelMapper.selectById(dto.getModelId()).getStatus());
					List<MechanismModelParam> modelParamList = mechanismModelParamService.list(Wrappers.<MechanismModelParam>query().lambda()
						.eq(MechanismModelParam::getModelId, dto.getModelId()).orderByAsc(MechanismModelParam::getParamType));
					if (Func.isNotEmpty(modelParamList)) {
						List<MechanismModelParamVO> paramList = modelParamList.stream().map(modelParam -> {
							MechanismModelParamVO modelParamVO = Objects.requireNonNull(BeanUtil.copy(modelParam, MechanismModelParamVO.class));
							modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParam.getParamType()))
								.setParamList(JSONUtil.toList(modelParam.getParamInfo(), ModelParamVO.class));
							return modelParamVO;
						}).collect(Collectors.toList());
						dto.setModelParamList(paramList);
					}
				}
				if (dto.getHasChildren() > 0) {
					List<MonitorModelDTO> childrenList = baseMapper.getChildrenList(dto.getId(), dto.getModelId());
					dto.setChildren(childrenList.stream().map(child -> {
						child.setMonitorId(dto.getId()).setModelId(child.getId());
						MonitorModelDTO childDTO = Objects.requireNonNull(BeanUtil.copy(child, MonitorModelDTO.class));
						childDTO.setApplyEquipmentName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, childDTO.getApplyEquipment()))
							.setApplyPowerName(DictBizCache.getValue(DictBizEnum.POWER_RANGE, childDTO.getApplyPower()))
							.setTypeName(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, childDTO.getType()))
							.setApplyDataName(DictBizCache.getValue(DictBizEnum.DATA_TYPE, childDTO.getApplyData()));
						List<MechanismModelParam> modelParams = mechanismModelParamService.list(Wrappers.<MechanismModelParam>query().lambda()
							.eq(MechanismModelParam::getModelId, child.getId()).orderByAsc(MechanismModelParam::getParamType));
						if (Func.isNotEmpty(modelParams)) {
							List<MechanismModelParamVO> paramList = modelParams.stream().map(modelParam -> {
								MechanismModelParamVO modelParamVO = Objects.requireNonNull(BeanUtil.copy(modelParam, MechanismModelParamVO.class));
								modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParam.getParamType()))
									.setParamList(JSONUtil.toList(modelParam.getParamInfo(), ModelParamVO.class));
								return modelParamVO;
							}).collect(Collectors.toList());
							childDTO.setModelParamList(paramList);
						}
						DictBiz childDictBiz = dictBizService.getDictValue(DictBizEnum.MODEL_TYPE.getName(), Func.toStr(childDTO.getType()));
						if (Func.isNotEmpty(childDictBiz)) {
							childDTO.setRemark(childDictBiz.getRemark());
						}
						childDTO.setModelStatus(mechanismModelMapper.selectById(childDTO.getModelId()).getStatus());
						return childDTO;
					}).collect(Collectors.toList()));
				}
			});
		}
		return page.setRecords(list);
	}

	@Override
	public IPage<MonitorModelDTO> monitorModelPage(IPage<MonitorModelDTO> page, MonitorModelVO monitorModel) {
		List<MonitorModelDTO> list = baseMapper.monitorModelPage(page, monitorModel);
		if (Func.isNotEmpty(list)) {
			list.forEach(dto -> {
				dto.setPathName(dto.getPathName().substring(0, dto.getPathName().lastIndexOf(StringPool.COMMA))
					.replace(StringPool.COMMA, StringPool.SLASH));
				MechanismModelDTO mechanismModel = JSONUtil.toBean(dto.getModelInfo(), MechanismModelDTO.class);
				dto.setApplyEquipment(mechanismModel.getApplyEquipment()).setApplyPower(mechanismModel.getApplyPower())
					.setApplyData(mechanismModel.getApplyData()).setType(mechanismModel.getType())
					.setApplyEquipmentName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, mechanismModel.getApplyEquipment()))
					.setApplyPowerName(DictBizCache.getValue(DictBizEnum.POWER_RANGE, mechanismModel.getApplyPower()))
					.setTypeName(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, mechanismModel.getType()))
					.setApplyDataName(DictBizCache.getValue(DictBizEnum.DATA_TYPE, mechanismModel.getApplyData()))
					.setModelStatus(mechanismModel.getStatus());
			});
		}
		return page.setRecords(list);
	}

}
