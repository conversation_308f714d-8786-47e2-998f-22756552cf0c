/*
 *      Copyright (c) 2018-2028
 */
package $!{package.ServiceImpl};

import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("entity","vo"))
#set($dtoPackage=$package.Entity.replace("entity","dto"))
import $!{dtoPackage}.$!{entity}Dto;
import $!{voPackage}.$!{entity}Vo;
import $!{package.Mapper}.$!{table.mapperName};
import $!{package.Service}.$!{table.serviceName};
import $!{superServiceImplClassPackage};
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @since $!{date}
 */
@AllArgsConstructor
@Service
#if($!{kotlin})
open class $!{table.serviceImplName} : $!{superServiceImplClass}<$!{table.mapperName}, $!{entity}, $!{entity}Dto, $!{entity}Vo>(), $!{table.serviceName} {

}
#else
public class $!{table.serviceImplName} extends $!{superServiceImplClass}<$!{table.mapperName}, $!{entity}, $!{entity}Dto, $!{entity}Vo> implements $!{table.serviceName} {


}
#end
