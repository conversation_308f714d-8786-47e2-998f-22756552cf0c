/*
 *      Copyright (c) 2018-2028
 */
#set($voPackage=$package.Entity.replace("entity","dto"))
package $!{voPackage};

import com.snszyk.core.crud.dto.BaseCrudDto;
#if($!{entityLombokModel})
import lombok.Data;
import lombok.EqualsAndHashCode;
#end
#if($!{swagger2})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end

/**
 * $!{table.comment}实体类
 *
 * <AUTHOR>
 * @since $!{date}
 */
#if($!{entityLombokModel})
@Data
#end
#if($!{superEntityClass})
@EqualsAndHashCode(callSuper = true)
#end
#if($!{swagger2})
@ApiModel(value = "$!{entity}Dto对象", description = #if ("$!{table.comment}"=="")"$!{entity}Dto对象"#else"$!{table.comment}"#end)
#end
#if($!{superEntityClass})
public class $!{entity}Dto extends BaseCrudDto {
#elseif($!{activeRecord})
@Accessors(chain = true)
public class $!{entity} extends Model<$!{entity}> {
#else
public class $!{superEntityClass} implements Serializable {
#end

## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in $!{table.fields})
#if($!{field.name}!=$!{cfg.tenantColumn})
#if($!{field.keyFlag})
#set($keyPropertyName=$!{field.propertyName})
#end
#if("$!field.comment" != "")
	/**
	* $!{field.comment}
	*/
	#if($!{swagger2})
	@ApiModelProperty(value = "$!{field.comment}")
	#end
#end
#if($!{field.keyFlag})
## 主键
#if($!{field.keyIdentityFlag})
	@TableId(value = "$!{field.name}", type = IdType.AUTO)
#elseif(!$null.isNull($!{idType}) && "$!idType" != "")
	@TableId(value = "$!{field.name}", type = IdType.$!{idType})
#elseif($!{field.convert})
	@TableId("$!{field.name}")
#end
## 普通字段
#elseif($!{field.fill})
## -----   存在字段填充设置   -----
#if($!{field.convert})
	@TableField(value = "$!{field.name}", fill = FieldFill.$!{field.fill})
#else
	@TableField(fill = FieldFill.$!{field.fill})
#end
#elseif($!{field.convert})
	@TableField("$!{field.name}")
#end
## 乐观锁注解
#if($!{versionFieldName}==$!{field.name})
	@Version
#end
## 逻辑删除注解
#if($!{logicDeleteFieldName}==$!{field.name})
	@TableLogic
#end
	private $!{field.propertyType} $!{field.propertyName};
#end
#end
## ----------  END 字段循环遍历  ----------

#if(!$!{entityLombokModel})
#foreach($field in $!{table.fields})
#if($!{field.propertyType.equals("boolean")})
#set($getprefix="is")
#else
#set($getprefix="get")
#end

	public $!{field.propertyType} $!{getprefix}$!{field.capitalName}() {
		return $!{field.propertyName};
	}

#if($!{entityBuilderModel})
	public $!{entity} set$!{field.capitalName}($!{field.propertyType} $!{field.propertyName}) {
#else
	public void set$!{field.capitalName}($!{field.propertyType} $!{field.propertyName}) {
#end
		this.$!{field.propertyName} = $!{field.propertyName};
#if($!{entityBuilderModel})
		return this;
#end
	}
#end
#end

#if($!{entityColumnConstant})
#foreach($field in $!{table.fields})
	public static final String $!{field.name.toUpperCase()} = "$!{field.name}";

#end
#end
#if($!{activeRecord})
	@Override
	protected Serializable pkVal() {
#if($!{keyPropertyName})
		return this.$!{keyPropertyName};
#else
		return this.id;
#end
	}

#end
#if(!$!{entityLombokModel})
	@Override
	public String toString() {
		return "$!{entity}{" +
#foreach($field in $!{table.fields})
#if($!{velocityCount}==1)
		"$!{field.propertyName}=" + $!{field.propertyName} +
#else
		", $!{field.propertyName}=" + $!{field.propertyName} +
#end
#end
		"}";
	}
#end
}
